<template>
  <vue-draggable-resizable
    :key="box?.key"
    v-if="box?.isOpen"
    :parent="true"
    :w="box?.width"
    :h="box?.height"
    :x="box?.x"
    :y="box?.y"
    :z="box?.z"
    :min-width="250"
    :min-height="250"
    :handles="handles"
    :draggable="layout == 'normal'"
    @resize="onResize"
    @drag="onDrag"
    @activated="activate"
    class="p-4 w-full bg-white shadow-lg rounded-md box"
    :class="layout"
    ref="draggableResizable"
  >
    <!-- Title & Close Button -->
    <div class="flex justify-between items-center border-b-2 border-gray-300 pb-2 mb-3">
      <div class="flex items-center gap-2">
        <button class="text-red-500 text-xl font-bold" @click="close">
          <XMarkIcon class="h-4 w-4" />
        </button>
        <button
          v-if="!box.isExpanded && layout == 'normal' && !encounterStore.isMobile"
          class="text-xl font-bold text-green-500"
          @click="expand"
          aria-label="Expand"
        >
          <ArrowsPointingOutIcon class="h-4 w-4" />
        </button>
        <button
          v-if="box.isExpanded && layout == 'normal' && !encounterStore.isMobile"
          class="text-xl font-bold text-green-500"
          @click="shrink"
          aria-label="Shrink"
        >
          <ArrowsPointingInIcon class="h-4 w-4" />
        </button>
      </div>
      <h2 class="text-primary font-bold text-lg" :class="{ history: isPatientView }">
        {{ title }}
      </h2>
    </div>
    <div class="overflow-y-auto" :style="{ height: contentHeight + 'px' }">
      <slot></slot>
    </div>
  </vue-draggable-resizable>
</template>
<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import 'vue-draggable-resizable/style.css'
import { XMarkIcon, ArrowsPointingOutIcon, ArrowsPointingInIcon } from '@heroicons/vue/24/outline'

defineEmits(['close', 'update-z', 'update-size'])
const props = withDefaults(
  defineProps<{
    type?: string
    title?: string
    layout?: 'left' | 'right' | 'normal'
  }>(),
  {
    type: '',
    title: '',
    layout: 'normal',
  },
)
import { useEncounterStore } from '@/stores/encounter'
import { useRoute } from 'vue-router'
const encounterStore = useEncounterStore()
const route = useRoute()
const isPatientView = computed(() => route.name === 'patient')

const box = computed(() => {
  const selectedBox = encounterStore.encounterBoxes.filter((x) => x.type == props.type)[0]
  if (!encounterStore.isMobile) {
    return selectedBox
  }
  return {
    type: selectedBox.type,
    isOpen: selectedBox.isOpen,
    height: encounterStore.parentContainerHeight,
    width: encounterStore.parentContainerWidth,
    x: 0,
    y: 0,
    z: selectedBox.z,
    isExpanded: selectedBox.isExpanded,
    oldX: selectedBox.oldX,
    oldY: selectedBox.oldY,
    key: selectedBox.key,
  }
})
const contentHeight = ref(box.value.height - 90)

const draggableResizable = ref()
const handles = ref(['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml'])

watch(
  () => box.value.isExpanded,
  (isExpanded) => {
    if (isExpanded) {
      draggableResizable.value!.width = encounterStore.parentContainerWidth
      draggableResizable.value!.height = encounterStore.parentContainerHeight
      contentHeight.value = encounterStore.parentContainerHeight - 90
    } else {
      draggableResizable.value!.width = box.value.width
      draggableResizable.value!.height = box.value.height
      contentHeight.value = box.value.height - 90
    }
  },
)

watch(
  () => box.value.height,
  (newValue) => {
    if (box.value.isExpanded) {
      const parent = document.querySelector('.parent')
      contentHeight.value = parent!.clientHeight - 90
    } else {
      contentHeight.value = newValue - 90
    }
  },
)

watch(
  () => box.value.isOpen,
  (newValue) => {
    if (newValue) {
      encounterStore.setActiveBox(box.value.type)
    }
  },
)

const close = () => {
  encounterStore.updateEncounterBoxes({
    ...box.value,
    isOpen: false,
  })
}

const onResize = (
  handle: ['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml'],
  x: number,
  y: number,
  width: number,
  height: number,
) => {
  encounterStore.updateEncounterBoxes({
    ...box.value,
    height: height,
    width: width,
    x: x,
    y: y,
    isExpanded: false,
    oldX: undefined,
    oldY: undefined,
  })
}
const onDrag = (x: number, y: number) => {
  encounterStore.updateEncounterBoxes({
    ...box.value,
    x: x,
    y: y,
  })
}
const setCenterPosition = () => {
  const parent = document.querySelector('.parent')
  const parentWidth = parent!.clientWidth
  const parentHeight = parent!.clientHeight

  encounterStore.updateEncounterBoxes({
    ...box.value,
    x: (parentWidth - box.value.width) / 2,
    y: (parentHeight - box.value.height) / 2,
  })
}

onMounted(() => {
  if (box.value.x == undefined) {
    setCenterPosition()
  }
  if (props.layout == 'left') {
    handles.value = ['mr']
  }
  if (props.layout == 'right') {
    handles.value = ['ml']
  }
  if (box.value.isExpanded && box.value.isOpen) {
    const parent = document.querySelector('.parent')
    draggableResizable.value!.width = parent!.clientWidth
    draggableResizable.value!.height = parent!.clientHeight
    contentHeight.value = parent!.clientHeight - 90
  }
})

const expand = () => {
  encounterStore.updateEncounterBoxes({
    ...box.value,
    x: 0,
    y: 0,
    isExpanded: true,
    oldX: box.value.x,
    oldY: box.value.y,
  })
}
const shrink = () => {
  encounterStore.updateEncounterBoxes({
    ...box.value,
    x: box.value.oldX ?? 0,
    y: box.value.oldY ?? 0,
    isExpanded: false,
    oldX: undefined,
    oldY: undefined,
  })
}

const activate = () => {
  encounterStore.setActiveBox(box.value.type)
}
</script>
<style scoped>
.left {
  @apply bg-primary;
  color: white;
  border-radius: 0%;
  border: none !important;
}
.left h2 {
  color: white;
}
.box {
  outline: none !important;
  border: 2px solid theme('colors.primary.DEFAULT') !important;
}
</style>
