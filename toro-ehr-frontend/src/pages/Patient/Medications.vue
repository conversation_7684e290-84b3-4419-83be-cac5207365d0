<template>
  <div class="py-2">
    <!-- Add Medication Form -->
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-6 mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Add New Medication</h3>

      <div class="space-y-4">
        <!-- Search Field -->
        <div>
          <AutoCompleteFluent
            v-model="selectedMedication"
            id="selectedMedication"
            label="Search medications"
            :options="medicationOptions"
            :hasMore="hasMore"
            optionLabel="displayName"
            @search="fetchMedications"
          />
        </div>

        <!-- Button Row -->
        <div class="flex justify-end">
          <Button
            icon="pi pi-plus"
            label="Add Medication"
            @click="addMedication"
            class="w-full md:w-auto"
            severity="primary"
          />
        </div>
      </div>
    </div>
    <!-- Medication History -->
    <div class="flex flex-col">
      <div class="mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Medication History</h3>
        <p class="text-sm text-gray-600">Your recorded medications</p>
      </div>

      <div class="-m-1.5 overflow-x-auto">
        <div class="p-1.5 min-w-full inline-block align-middle">
          <div class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-start">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                      Description
                    </span>
                  </th>

                  <th scope="col" class="px-6 py-3 text-end">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                      Actions
                    </span>
                  </th>
                </tr>
              </thead>

              <tbody class="divide-y divide-gray-200">
                <tr v-for="(med, index) in patientMedications" :key="med.code">
                  <td class="h-px w-72 whitespace-nowrap">
                    <div class="px-6 py-3">
                      <span class="block text-sm text-gray-500">{{ med.displayName }}</span>
                    </div>
                  </td>

                  <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
                    <div class="flex justify-center items-center">
                      <Button
                        icon="pi pi-trash"
                        variant="text"
                        severity="danger"
                        @click="confirmDialog?.open(index)"
                      />
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ConfirmDialog
    ref="confirmDialog"
    @confirmedAction="removeMedication"
    title="Delete Medication"
    message="Are you sure you want to delete this medication from your list?"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import Button from 'primevue/button'
import ConfirmDialog from '@/components/form-extensions/ConfirmDialog.vue'
import { api } from '@/api'
import { usePatientStore } from '@/stores/patient.ts'
import type { PatientMedicationResponse } from '@/api/api-reference.ts'
import { useToast } from 'vue-toastification'
import AutoCompleteFluent from '../../components/form-extensions/AutoCompleteFluent.vue'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const confirmDialog = ref<any | null>(null)

const patientStore = usePatientStore()
const toast = useToast()

onMounted(() => {
  if (patientStore.patientProfile) {
    patientMedications.value = patientStore.patientProfile!.medications
  }
})

const selectedMedication = ref()
const medicationOptions = ref()
const patientMedications = ref<PatientMedicationResponse[] | undefined>([])
const hasMore = ref(true)
const fetchMedications = async (query: string, page: number, reset = false) => {
  const medicationsResponse = await api.medications.medicationListMedications({
    searchParam: query,
    pageNumber: page,
  })

  if (reset) medicationOptions.value = []
  medicationOptions.value = [
    ...medicationOptions.value,
    ...(medicationsResponse.data.items?.map((x) => ({
      displayName: `${x.displayName}`,
      code: `${x.code}`,
    })) ?? []),
  ]
  hasMore.value = medicationOptions.value.length < (medicationsResponse.data.totalItems ?? 0)
}

const addMedication = async () => {
  try {
    patientMedications.value!.push(selectedMedication.value)
    selectedMedication.value = undefined
    await api.patients.patientSetMedications(patientMedications.value!)
    await patientStore.getPatientProfile()
    toast.success('Medication added successfully')
  } catch (error) {
    console.error('Error adding medication:', error)
    toast.error('Failed to add medication')
  }
}

const removeMedication = async (index: number) => {
  try {
    patientMedications.value!.splice(index, 1)
    await api.patients.patientSetMedications(patientMedications.value!)
    await patientStore.getPatientProfile()
    toast.success('Medication removed successfully')
  } catch (error) {
    console.error('Error removing medication:', error)
    toast.error('Failed to remove medication')
  }
}

watch(
  () => patientStore.patientProfile,
  (patient) => {
    if (patient) {
      patientMedications.value = patient.medications
    }
  },
)
</script>
