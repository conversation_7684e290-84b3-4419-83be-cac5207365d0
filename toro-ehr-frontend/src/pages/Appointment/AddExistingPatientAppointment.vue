<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[99]"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-bold">Existing patient</h2>
        <span
          class="text-primary underline cursor-pointer text-sm flex"
          @click="$emit('switch-form')"
        >
          New Patient <ArrowUpRightIcon class="size-3 m-1"
        /></span>
      </div>
      <div class="overflow-y-auto max-h-[70vh]">
        <form @submit.prevent="createExistingPatientAppointment">
          <div class="relative">
            <div class="my-3">
              <AutoCompleteFluent
                v-model="selectedPatient"
                id="selectedPatient"
                label="Search patients"
                :options="patientOptions"
                :hasMore="hasMore"
                optionLabel="optionText"
                @search="fetchPatients"
                @item-select="onPatientSelect"
                :invalid="!!errorMessage"
              />
            </div>
            <transition name="fade">
              <small v-if="errorMessage" class="error-message">
                {{ errorMessage }}
              </small>
            </transition>
          </div>
          <Select
            id="employeeId"
            label="Practitioner"
            :options="appointmentsStore.practitionerLookups"
          />
          <InputNumber id="durationInMinutes" label="Duration" suffix=" minutes" />
          <DateTimePicker
            id="startAt"
            label="Start"
            showTime
            hourFormat="12"
            dateFormat="m/d/yy"
            :stepMinute="5"
          />

          <!-- Conflict Warning Message -->
          <div
            v-if="showConflictWarning"
            class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4"
          >
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fill-rule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Existing Appointment Found</h3>
                <div class="mt-2 text-sm text-yellow-700">
                  <p>{{ conflictMessage }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-end">
            <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
              Cancel
            </button>
            <button
              @click="createExistingPatientAppointment"
              class="bg-primary text-white px-4 py-2 rounded"
            >
              {{ showConflictWarning ? 'Confirm Anyway' : 'Save' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm, useField } from 'vee-validate'
import { ref, watch } from 'vue'
import { api } from '@/api'
import type {
  CreateExistingPatientAppointmentCommand,
  PatientResponse,
  ExistingAppointmentResponse,
} from '@/api/api-reference.ts'
import * as yup from 'yup'
import { useAppointmentsStore } from '@/stores/appointments.ts'
import InputNumber from '../../components/form-extensions/InputNumberFluent.vue'
import DateTimePicker from '../../components/form-extensions/DateTimePickerFluent.vue'
import Select from '../../components/form-extensions/SelectFluent.vue'
import { type AutoCompleteOptionSelectEvent } from 'primevue/autocomplete'
import { ArrowUpRightIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'
import { formatDateWithoutTime } from '@/utils/timeMethods.ts'
import moment from 'moment-timezone'
import AutoCompleteFluent from '../../components/form-extensions/AutoCompleteFluent.vue'
import { useToast } from 'vue-toastification'
const toast = useToast()
const authStore = useAuthStore()
const shift = ref(0)

interface PatientSelectListitem {
  id: string
  optionText: string
  selectedText: string
}

const appointmentsStore = useAppointmentsStore()

const emit = defineEmits(['close', 'switch-form'])
const props = defineProps<{
  isModalOpen: boolean
  startDate: Date | null
}>()

const patientOptions = ref<PatientSelectListitem[]>([])
const selectedPatient = ref('')
const showConflictWarning = ref(false)
const conflictMessage = ref('')
const pendingFormData = ref<CreateExistingPatientAppointmentCommand | null>(null)

// Handle selection
const onPatientSelect = (event: AutoCompleteOptionSelectEvent) => {
  const patient = event.value as PatientSelectListitem
  selectedPatient.value = patient.selectedText
  setValue(patient.id)
}

const patients = ref<PatientResponse[]>([])

const getInitialValues = () => ({
  patientId: '',
  employeeId: '',
  durationInMinutes: 15,
  startAt: new Date(),
  locationId: authStore.user!.locationId!,
})

const { handleSubmit, resetForm, setFieldValue } = useForm({
  validationSchema: yup.object({
    patientId: yup.string().required('Patient is required'),
    employeeId: yup.string().required('Practitioner is required'),
    locationId: yup.string().required('Location is required'),
    durationInMinutes: yup.number().required('Duration is required'),
    startAt: yup.string().required('Start at is required'),
  }),
  initialValues: getInitialValues(),
})

const { setValue, errorMessage } = useField<string | null>('patientId')

// check for existing appointments
const checkForExistingAppointment = async (
  patientId: string,
  employeeId: string,
  date: string,
): Promise<ExistingAppointmentResponse | null> => {
  try {
    const response = await api.appointments.appointmentCheckExistingAppointment({
      patientId,
      employeeId,
      date,
    })
    return response.data
  } catch (error) {
    console.log('Error checking for existing appointment:', error)
    return null
  }
}

// create appointment after conflict check
const createExistingPatientAppointment = handleSubmit(async (values) => {
  try {
    const form: CreateExistingPatientAppointmentCommand = {
      ...values,
      durationInMinutes: Number(values.durationInMinutes),
      startAt: moment(values.startAt).subtract(shift.value, 'minutes').toISOString(),
      initiatedByPatient: false,
    }

    // if user already confirmed, proceed with creation
    if (showConflictWarning.value) {
      await proceedWithAppointmentCreation(form)
      return
    }

    // check for existing appointment
    const existingAppointment = await checkForExistingAppointment(
      form.patientId!,
      form.employeeId!,
      form.startAt!,
    )

    if (existingAppointment && existingAppointment.appointmentId) {
      // store form data for later use
      pendingFormData.value = form

      // format the conflict message
      const appointmentDate = new Date(existingAppointment.startAt!).toLocaleDateString()
      const appointmentTime = new Date(existingAppointment.startAt!).toLocaleTimeString([], {
        hour: 'numeric',
        minute: '2-digit',
      })

      conflictMessage.value = `There is already an appointment scheduled for this patient with ${existingAppointment.employeeName} on ${appointmentDate} at ${appointmentTime} at ${existingAppointment.locationName}.`

      // show warning in the modal
      showConflictWarning.value = true
    } else {
      // no conflict, proceed with creation
      await proceedWithAppointmentCreation(form)
      toast.success('Appointment created successfully.')
    }
  } catch (error) {
    console.log(error)
  }
})

// actual appointment creation
const proceedWithAppointmentCreation = async (form: CreateExistingPatientAppointmentCommand) => {
  try {
    await api.appointments.appointmentCreateForExistingPatient(form)
    resetForm()
    selectedPatient.value = ''
    showConflictWarning.value = false
    conflictMessage.value = ''
    pendingFormData.value = null
    emit('close')
  } catch (error) {
    console.log('Error creating appointment:', error)
  }
}

const hasMore = ref(true)
const fetchPatients = async (query: string, page: number, reset = false) => {
  if (!query.trim()) {
    patients.value = []
    return
  }
  const response = await api.patients.patientListPatients({
    searchParam: query,
    pageNumber: page,
  })

  if (reset) patients.value = []
  if (reset) patientOptions.value = []
  patients.value = [...patients.value, ...(response.data.items ?? [])]
  patientOptions.value = [
    ...patientOptions.value,
    ...(response.data.items?.map((x) => ({
      id: x.id!,
      optionText: `${x.firstName} ${x.lastName} - DOB: ${formatDateWithoutTime(x.birthday)}`,
      selectedText: `${x.firstName} ${x.lastName}`,
    })) ?? []),
  ]
  hasMore.value = patients.value.length < (response.data.totalItems ?? 0)
}

watch(
  () => props.isModalOpen,
  async (newValue) => {
    if (newValue) {
      resetForm()
      showConflictWarning.value = false
      conflictMessage.value = ''
      pendingFormData.value = null
      if (props.startDate) {
        const timezoneMoment = moment.tz(props.startDate, authStore.user!.timeZone ?? 'local')
        const timezoneOffset = timezoneMoment.utcOffset()
        const localOffset = moment().utcOffset()
        shift.value = timezoneOffset - localOffset

        const adjusted = timezoneMoment.add(shift.value, 'minutes').toDate()

        setFieldValue('startAt', adjusted)
      }
      const employeResponse = await api.employees.employeeGetLocationEmployee(
        authStore.user!.employeeId!,
        authStore.user!.locationId!,
      )
      setFieldValue('durationInMinutes', employeResponse.data.appointmentDurationInMinutes ?? 15)
    }
  },
)
</script>
