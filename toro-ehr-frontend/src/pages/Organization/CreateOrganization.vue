<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
      <h2 class="text-lg font-bold mb-4">Create Organization</h2>

      <!-- Form -->
      <form @submit.prevent="createOrUpdateOrganization">
        <InputText id="organizationName" label="Organization Name" />
        <InputText id="locationName" label="Default Location Name" />
        <SelectFluent id="type" label="Organization Type" :options="organizationTypes" />
        <InputText id="organizationAdmin.firstName" label="Organization Admin First Name" />
        <InputText id="organizationAdmin.lastName" label="Organization Admin Last Name" />
        <InputText id="organizationAdmin.email" label="Organization Admin Email" />
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button type="submit" class="bg-primary text-white px-4 py-2 rounded">Save</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { api } from '@/api'
import InputText from '@/components/form-extensions/InputTextFluent.vue'
import type { CreateOrganizationCommand, SelectListItem } from '@/api/api-reference'
import * as yup from 'yup'
import SelectFluent from '@/components/form-extensions/SelectFluent.vue'

enum OrganizationType {
  Hospital = 'Hospital',
  NonPrescribingOrganization = 'Non-Prescribing Organization',
  PrescribingOrganization = 'Prescribing  Organization',
}

const organizationTypes: SelectListItem[] = Object.entries(OrganizationType).map(
  ([key, value]) => ({
    value: key,
    text: value,
  }),
)

const emit = defineEmits(['close'])
defineProps<{
  isModalOpen: boolean
}>()

const { handleSubmit } = useForm<CreateOrganizationCommand>({
  validationSchema: yup.object({
    organizationName: yup.string().required('Organization name is required'),
    type: yup.string().required('Type is required'),
    locationName: yup.string().required('Location name is required'),
    organizationAdmin: yup.object().shape({
      firstName: yup.string().required('First name is required'),
      lastName: yup.string().required('Last name is required'),
      email: yup.string().email('Invalid email format').required('Email is required'),
    }),
  }),
})

const createOrUpdateOrganization = handleSubmit(async (values) => {
  try {
    await api.organizations.organizationCreate(values)
    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
