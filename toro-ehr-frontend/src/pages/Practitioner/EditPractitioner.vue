<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <h2 class="text-lg font-bold mb-4">
        {{ id ? 'Update Employee' : 'Create Employee' }}
      </h2>
      <div class="overflow-y-auto max-h-[70vh]">
        <!-- Form -->
        <form @submit.prevent="createOrUpdateEmployee">
          <InputText id="firstName" label="First Name" />
          <InputText id="lastName" label="Last Name" />
          <InputText id="email" label="Email" />
          <Select id="timeZone" label="Time Zone" :options="timeZones" />

          <div class="relative">
            <label class="text-sm text-gray-500">Role</label>
            <div class="flex flex-wrap gap-2 mb-4 ml-2">
              <div v-for="role in availableRoles" :key="role" class="flex items-center gap-2">
                <Checkbox v-model="roles" :inputId="`role-${role}`" name="roles" :value="role" />
                <label :for="`role-${role}`"> {{ pascalToSpaced(role) }} </label>
              </div>
            </div>
            <div>
              <transition name="fade">
                <small v-if="errors.roles" class="!-bottom-4 error-message">
                  {{ errors.roles }}
                </small>
              </transition>
            </div>
          </div>

          <InputText id="npi" label="NPI" />
          <InputText id="phoneNumber" label="Phone" />
          <InputText id="address.street" label="Street" />
          <InputText id="address.city" label="City" />
          <Select id="address.state" label="State" :options="states" />
          <InputText id="address.zipCode" label="Zip" />
          <div class="flex justify-end">
            <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
              Cancel
            </button>
            <button @click="createOrUpdateEmployee" class="bg-primary text-white px-4 py-2 rounded">
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm, useField } from 'vee-validate'
import { watch } from 'vue'
import { api } from '../../api'
import type { CreateEmployeeCommand, EmployeeResponse } from '../../api/api-reference'
import * as yup from 'yup'
import { getAllTimeZones } from '../../utils/tomeZones'
import { getAllStates } from '../../utils/states'
import { useAuthStore } from '@/stores/auth'
import InputText from '../../components/form-extensions/InputTextFluent.vue'
import Select from '../../components/form-extensions/SelectFluent.vue'
import Checkbox from 'primevue/checkbox'
import { pascalToSpaced } from '@/utils/stringUtils'
import { parsePhoneNumberFromString } from 'libphonenumber-js'

const authStore = useAuthStore()

const emit = defineEmits(['close'])
const props = defineProps<{
  practitioner: EmployeeResponse | null
  isModalOpen: boolean
}>()

const availableRoles = ['OrganizationAdmin', 'LocationAdmin', 'Practitioner', 'Nurse', 'HelpDesk']
const timeZones = getAllTimeZones()
const states = getAllStates()

const initialValues = {
  organizationId: authStore.user!.organizationId!,
  locationId: authStore.user!.locationId!,
  roles: [],
  email: '',
  firstName: '',
  lastName: '',
  npi: '',
  phoneNumber: '',
  timeZone: '(GMT-06:00) Central Time (US & Canada)',
  calendarColor: '',
  address: {
    street: '',
    city: '',
    state: '',
    zipCode: '',
  },
}

const schema = yup.object({
  firstName: yup.string().required('First Name is required'),
  lastName: yup.string().required('Last Name is required'),
  email: yup.string().required('Email is required').email('Invalid email format'),
  roles: yup
    .array()
    .of(yup.string().required()) // Ensure all roles are non-empty strings
    .min(1, 'At least one role is required')
    .required('Roles are required'), // Ensure `roles` is always defined
  npi: yup.string().when('roles', {
    is: (roles: string[] | undefined) => Array.isArray(roles) && roles.includes('Doctor'),
    then: (schema) =>
      schema.required('NPI is required for doctors').matches(/^\d{9}$/, 'Must be exactly 9 digits'),
    otherwise: (schema) => schema.notRequired(),
  }),
  timeZone: yup.string().required(),
  phoneNumber: yup
    .string()
    .trim()
    .nullable()
    .test('is-valid-phone', 'Invalid phone number', (value) => {
      if (!value) return true
      const phone = parsePhoneNumberFromString(value, 'US')
      return !!(phone && phone.isValid())
    }),
  address: yup.object().shape({
    street: yup.string().nullable(),
    city: yup.string().nullable(),
    state: yup.string().nullable(),
    zipCode: yup.string().nullable(),
  }),
})

const { handleSubmit, errors, setValues, resetForm } = useForm<CreateEmployeeCommand>({
  validationSchema: schema,
  initialValues: initialValues,
})

const { value: id } = useField<string | null>('id')
const { value: roles } = useField<string | null>('roles')

watch(
  () => props.practitioner,
  (newValue) => {
    resetForm()
    if (newValue) {
      const form = {
        ...newValue,
        address: newValue.address ?? {
          street: '',
          city: '',
          state: '',
          zipCode: '',
        },
      }
      setValues(form)
    }
  },
)

const createOrUpdateEmployee = handleSubmit(async (values) => {
  try {
    if (id.value) {
      await api.employees.employeeEdit(values)
    } else {
      await api.employees.employeeCreate(values)
    }
    resetForm()
    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
