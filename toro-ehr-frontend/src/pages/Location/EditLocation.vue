<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-2xl">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-lg font-bold">
          {{ location ? 'Update Location' : 'Create Location' }}
        </h2>
        <button type="button" class="text-gray-500 hover:text-gray-700 p-1" @click="$emit('close')">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
      </div>
      <div class="overflow-y-auto max-h-[70vh]">
        <!-- Form -->
        <form @submit.prevent="createOrUpdateLocation">
          <!-- Two-column layout -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Left Column - Basic Information -->
            <div class="space-y-4">
              <h3 class="text-md font-semibold text-gray-700 border-b pb-2">Basic Information</h3>
              <InputText id="name" label="Name" wrapperClass="mb-2" />
              <Select
                id="classification"
                label="Classification"
                wrapperClass="mb-2"
                :options="classificationOptions"
              />
              <Checkbox id="isDefault" label="Default" wrapperClass="mb-2" v-model="isDefault" />

              <h4 class="text-sm font-semibold text-gray-600 mt-6 mb-2">Address</h4>
              <InputText id="address.street" label="Street Address" wrapperClass="mb-2" />
              <InputText id="address.city" label="City" wrapperClass="mb-2" />
              <Select id="address.state" label="State" wrapperClass="mb-2" :options="states" />
              <InputText id="address.zipCode" label="Zip" wrapperClass="mb-2" />
            </div>

            <!-- Right Column - Configuration -->
            <div class="space-y-4">
              <h3 class="text-md font-semibold text-gray-700 border-b pb-2">Configuration</h3>
              <InputText id="phoneNumber" label="Phone" wrapperClass="mb-2" />
              <InputText
                id="taxIdentificationNumber"
                label="Tax Identification Number"
                wrapperClass="mb-2"
              />

              <h4 class="text-sm font-semibold text-gray-600 mt-6 mb-2">Appointment Settings</h4>
              <InputNumber
                id="markMissedTime"
                label="Mark appointment missed (hours)"
                wrapperClass="mb-2"
                v-tooltip="
                  'Number of hours after the scheduled appointment time when the system will automatically mark the appointment as missed  (default is 4 hours)'
                "
              />
              <InputNumber
                id="missedFee"
                label="Missed appointment fee"
                wrapperClass="mb-2"
                v-tooltip="
                  'Fee amount changed to patients when they miss an appointment or cancel within the allowed check-in window'
                "
              />
              <InputNumber
                id="checkInStartOffsetHours"
                label="Allow check-in (hours)"
                wrapperClass="mb-2"
                v-tooltip="
                  'Number of hours prior to appointment time that patient is allowed to check-in (default is 24 hours)'
                "
              />
              <Select
                id="sameDayAppointment"
                label="Same day appointments"
                wrapperClass="mb-2"
                :options="yesNoOptions"
                v-tooltip="'Allow patients to schedule same-day appointments'"
              />

              <h4 class="text-sm font-semibold text-gray-600 mt-6 mb-2">IPOS Settings</h4>
              <InputText id="iposTpn" label="IPOS TPN" wrapperClass="mb-2" />
              <InputText id="iposAuthKey" label="IPOS Auth Key" wrapperClass="mb-2" />
              <InputText id="iposCloudTpn" label="IPOS Cloud TPN" wrapperClass="mb-2" />
              <InputText id="iposCloudAuthKey" label="IPOS Cloud Auth Key" wrapperClass="mb-2" />
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end mt-8 pt-4 border-t">
            <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
              Cancel
            </button>
            <button @click="createOrUpdateLocation" class="bg-primary text-white px-4 py-2 rounded">
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useField, useForm } from 'vee-validate'
import { ref, watch } from 'vue'
import { api } from '../../api'
import InputText from '../../components/form-extensions/InputTextFluent.vue'
import Checkbox from '../../components/form-extensions/Checkbox.vue'
import Select from '../../components/form-extensions/SelectFluent.vue'
import type { LocationResponse } from '../../api/api-reference'
import * as yup from 'yup'
import { getAllStates } from '../../utils/states'
import InputNumber from '../../components/form-extensions/InputNumberFluent.vue'

const emit = defineEmits(['close'])
const props = defineProps<{
  location: LocationResponse | null
  isModalOpen: boolean
}>()

const yesNoOptions = ref([
  { text: 'Yes', value: true },
  { text: 'No', value: false },
])

const states = getAllStates()
const classificationOptions = [
  { text: 'Ambulatory', value: 'Ambulatory' },
  { text: 'Emergency', value: 'Emergency' },
  { text: 'Field', value: 'Field' },
  { text: 'Home health', value: 'Home health' },
  { text: 'Inpatient encounter', value: 'Inpatient encounter' },
  { text: 'Inpatient acute', value: 'Inpatient acute' },
  { text: 'Inpatient non-acute', value: 'Inpatient non-acute' },
  { text: 'Observation encounter', value: 'Observation encounter' },
  { text: 'Pre-admission', value: 'Pre-admission' },
  { text: 'Short stay', value: 'Short stay' },
  { text: 'Virtual', value: 'Virtual' },
]

const getInitialFormData = () => ({
  name: '',
  classification: '',
  isDefault: false,
  phoneNumber: '',
  taxIdentificationNumber: '',
  markMissedTime: 4,
  missedFee: 0,
  checkInStartOffsetHours: 24,
  sameDayAppointment: false,
  iposTpn: '',
  iposAuthKey: '',
  iposCloudTpn: '',
  iposCloudAuthKey: '',
  address: {
    street: '',
    city: '',
    state: '',
    zipCode: '',
  },
})

const { handleSubmit, setValues, resetForm } = useForm({
  validationSchema: yup.object({
    name: yup.string().required('Name is required'),
    classification: yup.string().required('Classification is required'),
    taxIdentificationNumber: yup.string().required('Tax Identification Number is required'),
    iposTpn: yup.string().required('IPOS TPN is required'),
    iposAuthKey: yup.string().required('IPOS Auth Key is required'),
    iposCloudTpn: yup.string().required('IPOS Cloud TPN is required'),
    iposCloudAuthKey: yup.string().required('IPOS Cloud Auth Key is required'),
  }),
  initialValues: getInitialFormData(),
})

const { value: id } = useField<boolean>('id')
const { value: isDefault } = useField<boolean>('isDefault')

// IPOS field bindings for proper validation
const { value: iposTpn } = useField<string>('iposTpn')
const { value: iposAuthKey } = useField<string>('iposAuthKey')
const { value: iposCloudTpn } = useField<string>('iposCloudTpn')
const { value: iposCloudAuthKey } = useField<string>('iposCloudAuthKey')

watch(
  () => props.location,
  (newValue) => {
    resetForm()
    if (newValue) {
      const form = {
        id: newValue.id,
        name: newValue.name,
        classification: newValue.classification ?? '',
        isDefault: newValue.isDefault,
        phoneNumber: newValue.phoneNumber,
        taxIdentificationNumber: newValue.taxIdentificationNumber,
        markMissedTime: newValue.markMissedTime,
        missedFee: newValue.missedFee,
        checkInStartOffsetHours: newValue.checkInStartOffsetHours,
        sameDayAppointment: newValue.sameDayAppointment ?? false,
        iposTpn: newValue.iposTpn ?? '',
        iposAuthKey: newValue.iposAuthKey ?? '',
        iposCloudTpn: newValue.iposCloudTpn ?? '',
        iposCloudAuthKey: newValue.iposCloudAuthKey ?? '',
        address: {
          street: newValue.address?.street ?? '',
          city: newValue.address?.city ?? '',
          state: newValue.address?.state ?? '',
          zipCode: newValue.address?.zipCode ?? '',
        },
      }
      setValues(form)
    }
  },
)

const createOrUpdateLocation = handleSubmit(async (values) => {
  try {
    if (id.value) {
      await api.locations.locationEdit(values)
    } else {
      await api.locations.locationCreate(values)
    }
    resetForm()
    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
