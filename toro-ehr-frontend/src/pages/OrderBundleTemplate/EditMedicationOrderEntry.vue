<template>
  <form id="rx-inline-form" @submit.prevent="submitOrderEntry(undefined)" class="contents">
    <Select id="medicationId" label="Clinical Drug or Pack" :options="medications" />
    <Select id="frequency" label="Frequency" :options="frequencies" v-model="frequencyValue" />
    <InputText v-if="customFrequencySelected" id="customFrequency" label="Custom Frequency" />
    <Select id="duration" label="Duration" :options="durations" />
    <Select id="prn" label="PRN" :options="prnOptions" v-model="prnValue" />
    <InputText v-if="prnSelected" id="prnReason" label="PRN reason" />
    <Select id="isRequired" label="Required" :options="prnOptions" />
    <button type="submit" class="hidden" aria-hidden="true" tabindex="-1"></button>
  </form>
  <div class="flex justify-end gap-2">
    <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="closeOrderEntry">
      Cancel
    </button>
    <Button
      v-if="editingOrderTemplateEntry"
      label="Edit Order"
      @click="submitOrderEntry(undefined)"
      class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white disabled:opacity-50 disabled:pointer-events-none"
    />
    <Button
      v-if="!editingOrderTemplateEntry"
      icon="pi pi-plus"
      label="Add Order"
      @click="toggleMenu"
      class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white disabled:opacity-50 disabled:pointer-events-none"
    />
    <Menu ref="menuRef" :model="steps" popup />
  </div>
</template>
<script setup lang="ts">
import { useField, useForm } from 'vee-validate'
import { computed, onMounted, ref, watch } from 'vue'
import { api } from '@/api'
import type {
  SearchOrderEntryResponse,
  OrderTemplateEntryResponse,
  SelectListItem,
  MedicationTemplateEntryResponse,
} from '@/api/api-reference'
import Select from '@/components/form-extensions/SelectFluent.vue'
import InputText from '@/components/form-extensions/InputTextFluent.vue'
import Button from 'primevue/button'
import * as yup from 'yup'
import { getFrequencies } from '@/utils/frequencies'
import { getDurations } from '@/utils/duration'
import Menu from 'primevue/menu'

const props = defineProps<{
  selectedOrderEntry: SearchOrderEntryResponse | undefined
  editingOrderTemplateEntry: OrderTemplateEntryResponse | undefined
  numberOfSteps: number
}>()

const emits = defineEmits(['close', 'submitMedicationOrderEntry'])

const prnValue = ref()
const frequencyValue = ref()
const prnSelected = computed(() => prnValue.value == true)
const customFrequencySelected = computed(() => frequencyValue.value == 'Custom')

const medications = ref<SelectListItem[]>([])
const frequencies = getFrequencies()

const durations = getDurations()

const prnOptions = ref([
  { text: 'Yes', value: true },
  { text: 'No', value: false },
])

const steps = computed(() => {
  const stepItems = []
  for (let step = 1; step <= props.numberOfSteps; step++) {
    stepItems.push({
      label: `Step ${step}`,
      command: () => {
        submitOrderEntry(step)
      },
    })
  }
  return stepItems
})

const menuRef = ref()
const toggleMenu = (event: MouseEvent) => {
  if (props.numberOfSteps <= 1) {
    submitOrderEntry(1)
  } else {
    menuRef.value.toggle(event)
  }
}

const initialValues = {
  medicationId: '',
  frequency: 'OnceDaily',
  duration: '5Days',
  prn: false,
  prnReason: '',
  isRequired: false,
}

const schema = yup.object({
  medicationId: yup.string().required('Medication is required'),
  frequency: yup.string().required('Frequency is required'),
  duration: yup.string().required('Duration is required'),
})

const { handleSubmit, setValues, resetForm } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const { setValue: setMedicationId } = useField<string | null>('medicationId')

function submitOrderEntry(stepNumber: number | undefined) {
  const submitFn = handleSubmit(async (values) => {
    stepNumber = stepNumber ?? 1
    emits('submitMedicationOrderEntry', values, stepNumber)
  })
  submitFn()
}

const closeOrderEntry = () => {
  resetForm()
  emits('close')
}

onMounted(async () => {
  if (props.selectedOrderEntry) {
    const response = await api.medications.medicationGetMedicationsByIngredient(
      props.selectedOrderEntry.code!,
    )
    medications.value = response.data ?? []
    setMedicationId(medications.value[0]?.value ?? null)
  }
  if (props.editingOrderTemplateEntry) {
    const medicationEditingOrder =
      props.editingOrderTemplateEntry as MedicationTemplateEntryResponse
    setValues({
      ...medicationEditingOrder,
      prnReason: medicationEditingOrder.prnReason ?? '',
    })
  }
})

watch(
  () => props.selectedOrderEntry,
  async (newValue) => {
    if (newValue) {
      const response = await api.medications.medicationGetMedicationsByIngredient(newValue.code!)
      medications.value = response.data ?? []
    }
  },
)

watch(
  () => props.editingOrderTemplateEntry,
  async (newValue) => {
    if (newValue) {
      const medicationEditingOrder = newValue as MedicationTemplateEntryResponse

      setValues({
        ...medicationEditingOrder,
        prnReason: medicationEditingOrder.prnReason ?? '',
      })
    }
  },
)
</script>
