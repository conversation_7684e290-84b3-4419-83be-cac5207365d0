<template>
  <div v-if="isVisible" class="p-3 rounded space-y-3">
    <form id="rx-inline-form" @submit.prevent="sendEmail" class="contents">
      <InputTextFluent id="subject" label="Subject" />

      <Textarea id="message" label="Discussed with patient" rows="10" cols="20" />
      <button type="submit" class="hidden" aria-hidden="true" tabindex="-1"></button>
    </form>
    <Button
      label="Send"
      @click="sendEmail"
      class="w-full font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-toroblue-600 focus:outline-none focus:bg-toroblue-600 disabled:opacity-50 disabled:pointer-events-none"
    />
  </div>
</template>
<script setup lang="ts">
import { useForm } from 'vee-validate'
import { useEncounterStore } from '@/stores/encounter.ts'
import { api } from '@/api'
import InputTextFluent from '@/components/form-extensions/InputTextFluent.vue'
import Textarea from '@/components/form-extensions/TextareaFluent.vue'
import Button from 'primevue/button'
import { watch } from 'vue'
import { useToast } from 'vue-toastification'
const toast = useToast()

const encounterStore = useEncounterStore()

defineProps<{ isVisible: boolean }>()
const emit = defineEmits(['close'])

const { handleSubmit, resetForm } = useForm()

const sendEmail = handleSubmit(async (values) => {
  try {
    const data = {
      ...values,
      MessageType: 'Call',
      PatientId: encounterStore.selectedEncounter!.patientId!,
      EncounterId: encounterStore.selectedEncounter?.id,
    }
    await api.encounter.encounterSendEncounterMessage(encounterStore.selectedEncounter!.id!, data)
    resetForm()
    await api.encounter.encounterGetCommunications(encounterStore.selectedEncounter!.id!)
    toast.success('Call recorded successfully')
    emit('close')
  } catch (error) {
    console.log(error)
  }
})

watch(
  () => encounterStore.selectedEncounter,
  () => {
    resetForm()
    emit('close')
  },
)
</script>
