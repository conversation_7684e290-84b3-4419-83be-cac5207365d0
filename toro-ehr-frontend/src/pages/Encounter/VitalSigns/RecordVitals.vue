<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-20"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
      <h2 class="text-lg font-bold mb-4">Record Vitals</h2>

      <!-- Form -->
      <form @submit.prevent="recordVitals('final')">
        <div v-for="measurement in selectedMeasurements" :key="measurement.type">
          <div v-if="measurement.valueType == 'number'" class="px-2">
            <InputNumber
              :id="measurement.type"
              :label="
                measurement.unit
                  ? `${measurement.textLong} (${measurement.unit})`
                  : measurement.textLong
              "
              mode="decimal"
              :minFractionDigits="0"
              :maxFractionDigits="5"
            />
          </div>
          <div v-if="measurement.valueType == 'bloodPressure'" class="px-2">
            <label class="block text-sm font-medium text-gray-700 mb-3" for="BloodPressure"
              >Blood Pressure</label
            >
            <InputMask
              id="BloodPressure"
              label=""
              :initialSystolic="systolic"
              :initialDiastolic="diastolic"
            />
          </div>
        </div>
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button
            @click.prevent="recordVitals('preliminary')"
            class="bg-primary text-white px-4 py-2 rounded mr-2"
          >
            Save
          </button>
          <button
            @click.prevent="recordVitals('final')"
            type="submit"
            class="bg-primary text-white px-4 py-2 rounded"
          >
            Record
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { api } from '@/api'
import { useEncounterStore } from '@/stores/encounter'
import type { Measurement } from '../../../utils/interfaces'
import InputNumber from '@/components/form-extensions/InputNumberFluent.vue'
import InputMask from '@/components/form-extensions/InputMaskFluent.vue'
import type { VitalSignRequest, VitalSignResponse } from '../../../api/api-reference'
import { onMounted, ref, watch } from 'vue'
import { useToast } from 'vue-toastification'
const toast = useToast()
const encounterStore = useEncounterStore()

const emit = defineEmits(['close'])
const props = defineProps<{
  isModalOpen: boolean
  selectedMeasurements: Measurement[]
  preliminaryVitalSigns: VitalSignResponse[]
}>()

const systolic = ref('')
const diastolic = ref('')

const { handleSubmit, setFieldValue } = useForm()

const recordVitals = (status: string) =>
  handleSubmit(async (values) => {
    const vitals: VitalSignRequest[] = Object.entries(values).flatMap(([key, value]) => {
      if (key === 'BloodPressure' && typeof value === 'string') {
        const [systolic, diastolic] = value.split('/').map((v) => v.trim())
        const preliminarySystolic = props.preliminaryVitalSigns.filter(
          (x) => x.type == 'SystolicBloodPressure',
        )[0]
        const preliminaryDiastolic = props.preliminaryVitalSigns.filter(
          (x) => x.type == 'DiastolicBloodPressure',
        )[0]

        return [
          {
            id: preliminarySystolic?.id,
            type: 'SystolicBloodPressure',
            value: systolic || undefined,
          },
          {
            id: preliminaryDiastolic?.id,
            type: 'DiastolicBloodPressure',
            value: diastolic || undefined,
          },
        ].filter((v) => v.value !== undefined)
      }
      const preliminary = props.preliminaryVitalSigns.filter((x) => x.type == key)[0]
      return value || preliminary?.id
        ? [{ id: preliminary?.id, type: key, value: value ? String(value) : '' }]
        : []
    })

    try {
      await api.encounter.encounterAddVitalSigns({
        vitals: vitals,
        patientId: encounterStore.selectedEncounter!.patientId!,
        encounterId: encounterStore.selectedEncounter?.id,
        status: status,
      })

      await encounterStore.getPatientVitalSigns(encounterStore.selectedEncounter!.patientId!)
      if (status == 'final') {
        toast.success('Vitals recorded successfully')
      }
      emit('close')
    } catch (error) {
      console.log(error)
    }
  })()

onMounted(() => {
  if (props.preliminaryVitalSigns) {
    props.preliminaryVitalSigns.forEach((vitalSign) => {
      setFieldValue(vitalSign.type!, vitalSign.value)
    })
  }
})

watch(
  () => props.preliminaryVitalSigns,
  (newValues) => {
    newValues.forEach((vitalSign) => {
      if (vitalSign.type == 'SystolicBloodPressure') {
        systolic.value = vitalSign.value!
      } else if (vitalSign.type == 'DiastolicBloodPressure') {
        diastolic.value = vitalSign.value!
      } else {
        setFieldValue(vitalSign.type!, vitalSign.value)
      }
    })
  },
)
</script>
