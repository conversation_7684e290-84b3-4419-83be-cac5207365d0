<template>
  <v-chart
    :option="chartOptions"
    ref="chartRef"
    style="height: 300px"
    :style="{ width: contentWidth + 'px' }"
  />
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch, watchEffect } from 'vue'
import { use } from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import { useEncounterStore } from '@/stores/encounter.ts'
import type { Measurement } from '@/utils/interfaces.ts'

const encounterStore = useEncounterStore()
const vitals = computed(() => {
  if (!encounterStore.vitalSigns) return []

  return encounterStore.vitalSigns
    .map((measurement) => ({
      date: measurement.recordedDate || '',
      type: measurement.type || '',
      value: measurement.value ? parseFloat(measurement.value) : NaN,
    }))
    .filter((item) => !isNaN(item.value))
})

use([<PERSON><PERSON><PERSON>, <PERSON>vas<PERSON><PERSON><PERSON>, Grid<PERSON>omponent, Tooltip<PERSON>omponent, Legend<PERSON>omponent])

const chartRef = ref<InstanceType<typeof VChart> | null>(null)
const props = defineProps<{ selectedMeasurement: Measurement[] }>()
const selectedLegend = ref<string | null>(null)

onMounted(() => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  chartRef.value?.chart?.on('legendselectchanged', (params: any) => {
    selectedLegend.value = params.name
  })
  if (box.value.isExpanded) {
    const parent = document.querySelector('.parent')
    contentWidth.value = parent!.clientWidth - 60
  }
  nextTick(() => {
    chartRef.value!.chart!.resize()
  })
})

const box = computed(() => encounterStore.encounterBoxes.filter((x) => x.type == 'vitals')[0])
const contentWidth = ref(box.value.width - 60)

watch(
  () => box.value.width,
  (newValue) => {
    if (box.value.isExpanded) {
      const parent = document.querySelector('.parent')
      contentWidth.value = parent!.clientWidth - 60
    } else {
      contentWidth.value = newValue - 60
    }
    nextTick(() => {
      chartRef.value!.chart!.resize()
    })
  },
)

watch(
  () => box.value.isExpanded,
  () => {
    if (box.value.isExpanded) {
      const parent = document.querySelector('.parent')
      contentWidth.value = parent!.clientWidth - 60
    } else {
      contentWidth.value = box.value.width - 60
    }
    nextTick(() => {
      chartRef.value!.chart!.resize()
    })
  },
)

watchEffect(() => {
  nextTick(() => {
    if (chartRef.value?.chart) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      chartRef.value.chart.on('legendselectchanged', (params: any) => {
        const selectedLegend = params.name

        const measurement = props.selectedMeasurement.find((m) => {
          if (m.type === 'BloodPressure') {
            return ['SBP', 'DBP'].includes(selectedLegend)
          }
          return m.textShort === selectedLegend
        })

        if (!measurement) return

        // Get corresponding vital types based on measurement
        const vitalTypes =
          measurement.type === 'BloodPressure'
            ? selectedLegend === 'SBP'
              ? ['SystolicBloodPressure']
              : ['DiastolicBloodPressure']
            : [measurement.type]

        const selectedData = vitals.value.filter((v) => vitalTypes.includes(v.type))

        if (selectedData.length > 0) {
          const dates = selectedData.map((d) => d.date).filter(Boolean)
          const min = dates.reduce((a, b) => (new Date(a) < new Date(b) ? a : b))
          const max = dates.reduce((a, b) => (new Date(a) > new Date(b) ? a : b))

          chartRef.value?.chart?.setOption({ xAxis: { min, max } })
        }
      })
    }
  })
})

const chartOptions = computed(() => {
  const selected = props.selectedMeasurement
  const legendData = selected.flatMap((m) =>
    m.type === 'BloodPressure' ? ['SBP', 'DBP'] : [m.textShort],
  )

  const selectedName = selectedLegend.value ?? legendData[0]

  const selectedMap = Object.fromEntries(legendData.map((name) => [name, name === selectedName]))

  const allSeriesData: { [key: string]: [string, number][] } = {}
  const series = selected.flatMap((m) => {
    if (m.type === 'BloodPressure') {
      const sbpData = vitals.value
        .filter((x) => x.type === 'SystolicBloodPressure')
        .map((item) => [item.date, item.value] as [string, number])
      allSeriesData['SBP'] = sbpData

      const dbpData = vitals.value
        .filter((x) => x.type === 'DiastolicBloodPressure')
        .map((item) => [item.date, item.value] as [string, number])
      allSeriesData['DBP'] = dbpData

      return [
        { name: 'SBP', type: 'line', data: sbpData },
        { name: 'DBP', type: 'line', data: dbpData },
      ]
    } else {
      const data = vitals.value
        .filter((x) => x.type === m.type)
        .map((item) => [item.date, item.value] as [string, number])
      allSeriesData[m.textShort] = data

      return [{ name: m.textShort, type: 'line', data }]
    }
  })

  // Only use dates from the selected series
  const selectedData = allSeriesData[selectedName] || []
  const categories = [...new Set(selectedData.map(([d]) => d))].sort(
    (a, b) => +new Date(a) - +new Date(b),
  )

  return {
    tooltip: {
      trigger: 'axis',
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: (params: any) => {
        const list = Array.isArray(params) ? params : [params]
        // On category axis, the x value is in `name` (or `axisValue`)
        const raw = list[0].axisValue ?? list[0].name

        // Format the date (choose your own format/locale)
        const d = new Date(raw)
        const header = d.toLocaleString(undefined, {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        })

        // Lines for each series at that point
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const lines = list.map((p: any) => `${p.marker} ${p.seriesName} <b>${p.value[1]}</b>`)

        return `${header}<br/>${lines.join('<br/>')}`
      },
    },
    legend: {
      data: legendData,
      selected: selectedMap,
      selectedMode: 'single',
      orient: 'vertical',
      left: 'left',
      top: 'center',
    },
    grid: { left: '10%', right: '1%', bottom: '3%', containLabel: true },
    xAxis: {
      type: 'category',
      data: categories,
      boundaryGap: false,
      axisTick: { alignWithLabel: true },
      axisLabel: {
        interval: 0, // show every data point label
        formatter: (val: string) => {
          const d = new Date(val)
          // MM/DD/YY -> adjust to your locale/format
          const mm = String(d.getMonth() + 1).padStart(2, '0')
          const dd = String(d.getDate()).padStart(2, '0')
          const yy = String(d.getFullYear()).slice(-2)
          return `${mm}/${dd}/${yy}`
        },
      },
    },
    yAxis: { type: 'value', scale: true },
    series,
  }
})
</script>
