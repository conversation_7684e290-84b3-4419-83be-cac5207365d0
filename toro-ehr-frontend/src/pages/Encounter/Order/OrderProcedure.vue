<template>
  <form id="rx-inline-form" @submit.prevent="addToQueue" class="contents">
    <InputText id="note" label="Notes" />
    <Select id="fasting" label="Fasting" :options="yesNoOptions" />
    <Select id="repeat" label="Repeat" :options="yesNoOptions" />
    <Select id="priority" label="Priority" :options="priorities" />
    <button type="submit" class="hidden" aria-hidden="true" tabindex="-1"></button>
  </form>
  <div class="flex justify-end">
    <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
      Cancel
    </button>
    <Button
      icon="pi pi-plus"
      label="Add to queue"
      @click="addToQueue"
      class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white disabled:opacity-50 disabled:pointer-events-none"
    />
  </div>
</template>
<script setup lang="ts">
import { useForm } from 'vee-validate'
import { onMounted, ref, watch } from 'vue'
import { useEncounterStore } from '@/stores/encounter'
import { api } from '@/api'
import type { LabOrderResponse, SearchOrderEntryResponse } from '@/api/api-reference'
import Select from '@/components/form-extensions/SelectFluent.vue'
import InputText from '@/components/form-extensions/InputTextFluent.vue'
import Button from 'primevue/button'
import * as yup from 'yup'
import { getPriorities } from '../../../utils/priority'

const encounterStore = useEncounterStore()

const props = defineProps<{
  selectedOrderEntry: SearchOrderEntryResponse | undefined
}>()

const emits = defineEmits(['close'])

const priorities = getPriorities()

const yesNoOptions = ref([
  { text: 'Yes', value: true },
  { text: 'No', value: false },
])

const initialValues = {
  note: '',
  fasting: false,
  repeat: false,
  priority: 'Routine',
}

const schema = yup.object({
  note: yup.string().nullable(),
  fasting: yup.bool().required('Fasting is required'),
  repeat: yup.bool().required('Repeat is required'),
  priority: yup.string().required('Priority is required'),
})

const { handleSubmit, setValues, resetForm } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const addToQueue = handleSubmit(async (values) => {
  try {
    let orderId
    if (encounterStore.editingOrder) {
      orderId = encounterStore.editingOrder.id
      await api.encounter.encounterEditOrderProcedure(encounterStore.selectedEncounter!.id!, {
        ...values,
        id: orderId,
        snomedCodeId: props.selectedOrderEntry!.id!,
        bundleId: encounterStore.editingBundleId,
      })
    } else {
      const response = await api.encounter.encounterCreateOrderProcedure(
        encounterStore.selectedEncounter!.id!,
        {
          ...values,
          snomedCodeId: props.selectedOrderEntry!.id!,
          encounterId: encounterStore.selectedEncounter!.id!,
          patientId: encounterStore.selectedEncounter!.patientId!,
        },
      )
      orderId = response.data
    }
    encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
    resetForm()
    encounterStore.setEditingOrder(null, null)
    emits('close', orderId)
  } catch (error) {
    console.log(error)
  }
})

onMounted(async () => {
  if (encounterStore.editingOrder) {
    const labEditingOrder = encounterStore.editingOrder as LabOrderResponse

    setValues({
      ...labEditingOrder,
      note: labEditingOrder.note ?? '',
    })
  }
})

watch(
  () => encounterStore.editingOrder,
  (newValue) => {
    if (newValue) {
      const labEditingOrder = newValue as LabOrderResponse

      setValues({
        ...labEditingOrder,
        note: labEditingOrder.note ?? '',
      })
    }
  },
)
</script>
