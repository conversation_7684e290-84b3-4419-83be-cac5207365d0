<template>
  <div v-if="isVisible">
    <div class="p-3 rounded space-y-3">
      <div class="flex gap-1 items-center my-3 w-full">
        <Button variant="link" @click="emit('close')">
          <ArrowUturnLeftIcon class="size-4" />
        </Button>
        <Select
          v-model="orderEntryType"
          id="orderEntryType"
          label="Type"
          :options="orderEntryTypes"
        />
        <AutoCompleteFluent
          v-model="selectedOrderEntry"
          id="selectedPatient"
          label="Order Entry"
          :options="orderEntryOptions"
          :hasMore="hasMore"
          optionLabel="displayName"
          @search="fetchOrderEntries"
          class="w-full"
        />
      </div>
    </div>
    <div class="px-4 sm:px-2 mx-auto">
      <div class="flex">
        <div class="w-1/2 p-2">
          <OrderMedication
            v-if="isMedicine"
            :selected-order-entry="selectedOrderEntry"
            @close="removeOrderEntry"
          />
          <OrderLab
            v-if="isLab"
            :selected-order-entry="selectedOrderEntry"
            @close="removeOrderEntry"
          />
          <OrderProcedure
            v-if="isProcedure"
            :selected-order-entry="selectedOrderEntry"
            @close="removeOrderEntry"
          />
          <OrderBundle
            v-if="isBundle"
            :selected-order-entry="selectedOrderEntry"
            @close="removeOrderEntry"
          />
          <OrderImmunization
            v-if="isImmunization"
            :selected-order-entry="selectedOrderEntry"
            @close="removeOrderEntry"
          />
        </div>
        <div v-if="draftOrders.length > 0" class="w-1/2 p-2">
          <h2>Queue</h2>
          <table class="min-w-full divide-y divide-gray-200">
            <tbody class="divide-y divide-gray-200">
              <template v-for="(row, index) in draftOrders" :key="index">
                <tr class="relative hover:bg-gray-100 cursor-pointer">
                  <td class="h-px whitespace-nowrap">
                    <div class="px-6 py-3">
                      <Checkbox
                        v-model="selectedRows"
                        :input-id="'row-' + index"
                        name="selectedRows"
                        :value="row.id"
                      />
                    </div>
                  </td>
                  <td class="h-px max-w-xs overflow-hidden whitespace-nowrap text-ellipsis">
                    <div class="flex px-6 py-3">
                      <span class="block text-sm text-gray-800 truncate" v-tooltip="row.name">{{
                        row?.name
                      }}</span>
                      <button
                        v-if="row.orderType == 'Bundle'"
                        class="ml-2 focus:outline-none transition-transform"
                        @click.stop="toggleRow(index)"
                      >
                        <ChevronDownIcon
                          class="w-5 h-5 text-gray-500 transform transition-transform duration-300"
                          :class="{ 'rotate-180': expandedRows.has(index) }"
                        />
                      </button>
                    </div>
                  </td>
                  <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
                    <div class="flex items-right gap-x-4">
                      <!-- Edit Button -->
                      <a
                        class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                        href="#"
                        @click.prevent="editOrder(row)"
                      >
                        <PencilSquareIcon class="w-4 h-4" />
                        Edit
                      </a>
                      <!-- Deactivate Button -->
                      <a
                        class="inline-flex items-center gap-x-1 text-sm text-red-500 font-medium hover:underline focus:outline-none focus:underline"
                        href="#"
                        @click.prevent="deleteOrder(row.id!)"
                      >
                        <TrashIcon class="w-4 h-4" />

                        Delete
                      </a>
                    </div>
                  </td>
                </tr>
                <template v-if="row.orderType == 'Bundle' && expandedRows.has(index)">
                  <tr
                    v-for="order in row.orders"
                    :key="order.id"
                    class="relative hover:bg-gray-100 cursor-pointer"
                  >
                    <td class="h-px whitespace-nowrap">
                      <div class="px-6 py-3"></div>
                    </td>
                    <td class="h-px max-w-xs overflow-hidden whitespace-nowrap text-ellipsis">
                      <div class="px-6 py-3">
                        <span
                          class="block text-sm text-gray-800 truncate pl-4"
                          v-tooltip="order.name"
                          >{{ order?.name }}</span
                        >
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
                      <div class="flex items-right gap-x-4">
                        <!-- Edit Button -->
                        <a
                          class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                          href="#"
                          @click.prevent="editOrder(order, row.id)"
                        >
                          <PencilSquareIcon class="w-4 h-4" />
                          Edit
                        </a>
                        <!-- Deactivate Button -->
                        <a
                          class="inline-flex items-center gap-x-1 text-sm text-red-500 font-medium hover:underline focus:outline-none focus:underline"
                          href="#"
                          @click.prevent="deleteOrder(order.id!, row.id)"
                        >
                          <TrashIcon class="w-4 h-4" />

                          Delete
                        </a>
                      </div>
                    </td>
                  </tr>
                </template>
              </template>
            </tbody>
          </table>
          <div v-if="encounterStore.encounterOrders.length > 0" class="flex justify-between">
            <button
              type="button"
              class="text-gray-500 px-4 py-2 mr-2 border-2 rounded-lg"
              @click="saveAsBundleOpen = true"
            >
              Save as Bundle
            </button>
            <Button
              label="Sign"
              @click="signOrders"
              class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-toroblue-600 focus:outline-none focus:bg-toroblue-600 disabled:opacity-50 disabled:pointer-events-none"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <SaveAsBundle
    :isModalOpen="saveAsBundleOpen"
    :selected-order-ids="selectedRows"
    @close="closeSaveAsBundle"
  />
</template>
<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useEncounterStore } from '../../../stores/encounter'
import { api } from '../../../api'
import type { OrderResponse, SearchOrderEntryResponse } from '../../../api/api-reference'
import OrderMedication from './OrderMedication.vue'
import OrderBundle from './OrderBundle.vue'
import OrderLab from './OrderLab.vue'
import OrderProcedure from './OrderProcedure.vue'
import OrderImmunization from './OrderImmunization.vue'
import Checkbox from 'primevue/checkbox'
import Button from 'primevue/button'
import {
  PencilSquareIcon,
  TrashIcon,
  ArrowUturnLeftIcon,
  ChevronDownIcon,
} from '@heroicons/vue/24/outline'
import SaveAsBundle from './SaveAsBundle.vue'
import AutoCompleteFluent from '@/components/form-extensions/AutoCompleteFluent.vue'
import Select from '@/components/form-extensions/SelectFluent.vue'
import { useToast } from 'vue-toastification'

const toast = useToast()
const encounterStore = useEncounterStore()

defineProps<{ isVisible: boolean }>()
const emit = defineEmits(['close'])

const selectedOrderEntry = ref<SearchOrderEntryResponse>()
const orderEntryType = ref<string>('Med')
const orderEntryTypes = [
  { text: 'med', value: 'Med' },
  { text: 'proc', value: 'Procedure' },
  { text: 'lab', value: 'Lab' },
  { text: 'imm', value: 'Immunization' },
  { text: 'bundle', value: 'Bundle' },
]
const orderEntryOptions = ref<SearchOrderEntryResponse[]>([])
const selectedRows = ref<string[]>([])

const isMedicine = computed(() => selectedOrderEntry.value?.type === 'Med')
const isLab = computed(() => selectedOrderEntry.value?.type === 'Lab')
const isProcedure = computed(() => selectedOrderEntry.value?.type === 'Procedure')
const isBundle = computed(() => encounterStore.editingOrder?.orderType === 'Bundle')
const isImmunization = computed(() => selectedOrderEntry.value?.type === 'Immunization')

const draftOrders = computed(() =>
  encounterStore.encounterOrders.filter((order) => order.status === 'Draft'),
)

const expandedRows = ref(new Set<number>())
function toggleRow(index: number) {
  if (expandedRows.value.has(index)) {
    expandedRows.value.delete(index) // close this one
  } else {
    expandedRows.value.add(index) // open this one
  }
}

const hasMore = ref(true)
const fetchOrderEntries = async (query: string, page: number, reset = false) => {
  encounterStore.setEditingOrder(null, null)
  if (!query.trim()) {
    return
  }
  const response = await api.bundleTemplates.orderBundleTemplateSearchOrderEntry({
    searchParam: query,
    pageNumber: page,
    type: orderEntryType.value,
  })

  if (reset) orderEntryOptions.value = []
  orderEntryOptions.value = [...orderEntryOptions.value, ...(response.data.items ?? [])]
  hasMore.value = orderEntryOptions.value.length < (response.data.totalItems ?? 0)
}

const editOrder = (row: OrderResponse, bundleId: string | null = null) => {
  selectedOrderEntry.value = row.orderEntry
  encounterStore.setEditingOrder(row, bundleId)
}

const deleteOrder = async (rowId: string, bundleId: string | null = null) => {
  await api.encounter.encounterDeleteOrder(encounterStore.selectedEncounter!.id!, {
    id: rowId,
    bundleId,
  })
  await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
  await encounterStore.getPatientActiveMedications(encounterStore.selectedEncounter!.patientId!)
}

const saveAsBundleOpen = ref(false)

const closeSaveAsBundle = async () => {
  saveAsBundleOpen.value = false
  selectedRows.value = []
  await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
  await encounterStore.getPatientActiveMedications(encounterStore.selectedEncounter!.patientId!)
}

const signOrders = async () => {
  if (selectedRows.value.length > 0) {
    await api.encounter.encounterChangeOrderStatus(encounterStore.selectedEncounter!.id!, {
      orderIds: selectedRows.value,
      status: 'Active',
    })
    await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
    await encounterStore.getPatientActiveMedications(encounterStore.selectedEncounter!.patientId!)

    toast.success(
      selectedRows.value.length > 1 ? 'Orders signed successfully' : 'Order signed successfully',
    )
  }
  emit('close')
}

const removeOrderEntry = (id: string) => {
  selectedOrderEntry.value = undefined
  if (id) {
    selectedRows.value.push(id)
  }
}

watch(
  () => encounterStore.editingOrder,
  (newValue) => {
    if (newValue) {
      selectedOrderEntry.value = newValue.orderEntry
    }
  },
)

watch(
  () => encounterStore.selectedEncounter?.patientId,
  () => {
    emit('close')
  },
)

watch(
  () => orderEntryType.value,
  () => (selectedOrderEntry.value = undefined),
)

watch(
  () => selectedOrderEntry.value,
  async (newValue) => {
    if (newValue?.type == 'Bundle' && !encounterStore.editingOrder) {
      await api.encounter.encounterCreateOrderBundle(encounterStore.selectedEncounter!.id!, {
        bundleTemplateId: newValue.id,
        encounterId: encounterStore.selectedEncounter!.id,
        patientId: encounterStore.selectedEncounter!.patientId,
      })
      await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
      await encounterStore.getPatientActiveMedications(encounterStore.selectedEncounter!.patientId!)
    }
  },
)
</script>
