<template>
  <form id="rx-inline-form" @submit.prevent="addToQueue" class="contents">
    <InputText id="note" label="Notes" />
    <button type="submit" class="hidden" aria-hidden="true" tabindex="-1"></button>
  </form>
  <div class="flex justify-end">
    <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
      Cancel
    </button>
    <Button
      icon="pi pi-plus"
      label="Add to queue"
      @click="addToQueue"
      class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-toroblue-600 focus:outline-none focus:bg-toroblue-600 disabled:opacity-50 disabled:pointer-events-none"
    />
  </div>
</template>
<script setup lang="ts">
import { useForm } from 'vee-validate'
import { onMounted, watch } from 'vue'
import { useEncounterStore } from '@/stores/encounter'
import { api } from '@/api'
import type {
  ImmunizationOrderResponse,
  LabOrderResponse,
  SearchOrderEntryResponse,
} from '@/api/api-reference'
import InputText from '@/components/form-extensions/InputTextFluent.vue'
import Button from 'primevue/button'
import * as yup from 'yup'

const encounterStore = useEncounterStore()

const props = defineProps<{
  selectedOrderEntry: SearchOrderEntryResponse | undefined
}>()

const emits = defineEmits(['close'])

const initialValues = {
  note: '',
}

const schema = yup.object({
  note: yup.string().nullable(),
})

const { handleSubmit, setValues, resetForm } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const addToQueue = handleSubmit(async (values) => {
  try {
    let orderId
    if (encounterStore.editingOrder) {
      orderId = encounterStore.editingOrder.id
      await api.encounter.encounterEditOrderImmunization(encounterStore.selectedEncounter!.id!, {
        ...values,
        id: orderId,
        immunizationId: props.selectedOrderEntry!.id!,
        bundleId: encounterStore.editingBundleId,
      })
    } else {
      const response = await api.encounter.encounterCreateOrderImmunization(
        encounterStore.selectedEncounter!.id!,
        {
          ...values,
          immunizationId: props.selectedOrderEntry!.id!,
          encounterId: encounterStore.selectedEncounter!.id!,
          patientId: encounterStore.selectedEncounter!.patientId!,
        },
      )
      orderId = response.data
    }
    await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
    resetForm()
    encounterStore.setEditingOrder(null, null)
    emits('close', orderId)
  } catch (error) {
    console.log(error)
  }
})

onMounted(async () => {
  if (encounterStore.editingOrder) {
    const labEditingOrder = encounterStore.editingOrder as ImmunizationOrderResponse

    setValues({
      ...labEditingOrder,
      note: labEditingOrder.note ?? '',
    })
  }
})

watch(
  () => encounterStore.editingOrder,
  (newValue) => {
    if (newValue) {
      const labEditingOrder = newValue as ImmunizationOrderResponse

      setValues({
        ...labEditingOrder,
        note: labEditingOrder.note ?? '',
      })
    }
  },
)
</script>
