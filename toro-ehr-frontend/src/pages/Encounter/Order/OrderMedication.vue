<template>
  <form id="rx-inline-form" @submit.prevent="addToQueue" class="contents">
    <Select id="medicationId" label="Clinical Drug or Pack" :options="medications" />
    <Select id="frequency" label="Frequency" :options="frequencies" v-model="frequencyValue" />
    <InputText v-if="customFrequencySelected" id="customFrequency" label="Custom Frequency" />
    <Select id="duration" label="Duration" :options="durations" />
    <Select id="prn" label="PRN" :options="prnOptions" v-model="prnValue" />
    <InputText v-if="prnSelected" id="prnReason" label="PRN reason" />
    <DateTimePicker id="startTime" label="Start time" showTime hourFormat="12" :stepMinute="15" />
    <InputText id="instructions" label="Instructions" />
    <button type="submit" class="hidden" aria-hidden="true" tabindex="-1"></button>
  </form>
  <div class="flex justify-end">
    <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
      Cancel
    </button>
    <Button
      icon="pi pi-plus"
      label="Add to queue"
      @click="addToQueue"
      class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-toroblue-600 focus:outline-none focus:bg-toroblue-600 disabled:opacity-50 disabled:pointer-events-none"
    />
  </div>
</template>
<script setup lang="ts">
import { useField, useForm } from 'vee-validate'
import { computed, onMounted, ref, watch } from 'vue'
import { useEncounterStore } from '../../../stores/encounter'
import { api } from '../../../api'
import type {
  MedicationOrderResponse,
  SearchOrderEntryResponse,
  SelectListItem,
} from '../../../api/api-reference'
import Select from '../../../components/form-extensions/SelectFluent.vue'
import InputText from '../../../components/form-extensions/InputTextFluent.vue'
import DateTimePicker from '../../../components/form-extensions/DateTimePickerFluent.vue'
import Button from 'primevue/button'
import * as yup from 'yup'
import { getFrequencies } from '@/utils/frequencies'
import { getDurations } from '../../../utils/duration'

const encounterStore = useEncounterStore()

const props = defineProps<{
  selectedOrderEntry: SearchOrderEntryResponse | undefined
}>()

const emits = defineEmits(['close'])

const prnValue = ref()
const frequencyValue = ref()
const prnSelected = computed(() => prnValue.value == true)
const customFrequencySelected = computed(() => frequencyValue.value == 'Custom')

const medications = ref<SelectListItem[]>([])
const frequencies = getFrequencies()

const durations = getDurations()

const prnOptions = ref([
  { text: 'Yes', value: true },
  { text: 'No', value: false },
])

const initialValues = {
  encounterId: encounterStore.selectedEncounter!.id!,
  patientId: encounterStore.selectedEncounter!.patientId!,
  medicationId: '',
  frequency: 'OnceDaily',
  customFrequency: '',
  duration: '5Days',
  prn: false,
  prnReason: '',
  startTime: new Date(),
  instructions: '',
}

const schema = yup.object({
  medicationId: yup.string().required('Medication is required'),
  frequency: yup.string().required('Frequency is required'),
  customFrequency: yup.string().nullable(),
  duration: yup.string().required('Duration is required'),
  startTime: yup.date().required('Start time is required'),
})

const { handleSubmit, setValues, resetForm } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const { setValue: setMedicationId } = useField<string | null>('medicationId')

const addToQueue = handleSubmit(async (values) => {
  try {
    let orderId
    if (encounterStore.editingOrder) {
      orderId = encounterStore.editingOrder.id
      await api.encounter.encounterEditOrderMedication(encounterStore.selectedEncounter!.id!, {
        ...values,
        id: orderId,
        ingredientId: props.selectedOrderEntry!.id!,
        startTime: values.startTime.toISOString(),
        bundleId: encounterStore.editingBundleId,
      })
    } else {
      const response = await api.encounter.encounterCreateOrderMedication(
        encounterStore.selectedEncounter!.id!,
        {
          ...values,
          ingredientId: props.selectedOrderEntry!.id!,
          startTime: values.startTime.toISOString(),
        },
      )
      orderId = response.data
    }
    await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
    await encounterStore.getPatientActiveMedications(encounterStore.selectedEncounter!.patientId!)

    resetForm()
    encounterStore.setEditingOrder(null, null)
    emits('close', orderId)
  } catch (error) {
    console.log(error)
  }
})

onMounted(async () => {
  if (props.selectedOrderEntry) {
    const response = await api.medications.medicationGetMedicationsByIngredient(
      props.selectedOrderEntry.code!,
    )
    medications.value = response.data ?? []
    setMedicationId(medications.value[0]?.value ?? null)
  }
  if (encounterStore.editingOrder) {
    const medicationOrder = encounterStore.editingOrder as MedicationOrderResponse
    setValues({
      ...encounterStore.editingOrder,
      prnReason: medicationOrder.prnReason ?? '',
      startTime: new Date(medicationOrder.startTime!),
      customFrequency: medicationOrder.customFrequency ?? '',
    })
  }
})

watch(
  () => props.selectedOrderEntry,
  async (newValue) => {
    if (newValue) {
      const response = await api.medications.medicationGetMedicationsByIngredient(newValue.code!)
      medications.value = response.data ?? []
    }
  },
)

watch(
  () => encounterStore.editingOrder,
  (newValue) => {
    if (newValue) {
      const medicationOrder = newValue as MedicationOrderResponse
      setValues({
        ...newValue,
        prnReason: medicationOrder.prnReason ?? '',
        startTime: new Date(medicationOrder.startTime!),
        customFrequency: medicationOrder.customFrequency ?? '',
      })
    }
  },
)
</script>
