<template>
  <div class="p-4">
    <SelectFluent
      id="template"
      v-model="encounterStore.selectedTemplateId"
      :options="templates"
      label="New Note From Template"
      optionLabel="label"
      optionValue="value"
      class="w-full"
    />

    <!-- Medical Codes Section - At the very top -->
    <div v-if="selectedTemplate?.fields" class="mt-6 space-y-4">
      <div class="flex items-center justify-between border-b border-gray-200 pb-2">
        <h3 class="text-lg font-semibold text-gray-800">Medical Codes</h3>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- ICD-10 Code Dropdown -->
        <div>
          <div class="my-3">
            <FloatLabel variant="on">
              <MultiSelect
                v-model="encounterStore.selectedIcdCodes"
                :options="icdCodeOptions"
                @filter="searchIcdCodes"
                @show="loadCommonIcdCodes"
                optionLabel="displayName"
                input-id="selectedIcdCodes"
                fluid
                filter
                :maxSelectedLabels="2"
              />
              <label for="selectedIcdCodes">ICD-10 codes</label>
            </FloatLabel>
          </div>
        </div>

        <!-- CPT Code Dropdown -->
        <div>
          <div class="my-3">
            <AutoCompleteFluent
              v-model="encounterStore.selectedCptCode"
              id="selectedCptCode"
              label="Time spent"
              :options="cptCodeOptions"
              @dropdown="loadCommonCptCodes"
              :hasMore="hasMore"
              optionLabel="clinicianDescriptor"
              @search="fetchCptCodes"
              dropdown
            />
          </div>
        </div>
      </div>

      <!-- Selected ICD Codes Table -->
      <div v-if="selectedIcdCodes.length > 0" class="mt-4">
        <h4 class="text-md font-medium text-gray-700 mb-2">Selected ICD-10 Codes</h4>
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Code</th>
                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Description</th>
                <th class="px-4 py-2 text-center text-sm font-medium text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr
                v-for="(code, index) in selectedIcdCodes"
                :key="code.code"
                class="hover:bg-gray-50"
              >
                <td class="px-4 py-2 text-sm font-mono text-gray-900">{{ code.code }}</td>
                <td class="px-4 py-2 text-sm text-gray-900">{{ getIcdDescription(code) }}</td>
                <td class="px-4 py-2 text-center">
                  <Button
                    icon="pi pi-trash"
                    size="small"
                    text
                    severity="danger"
                    @click="removeIcdCode(index)"
                    aria-label="Delete ICD code"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div v-if="selectedTemplate?.fields" class="mt-4 space-y-4">
      <div v-for="field in selectedTemplate.fields" :key="field.name">
        <div class="flex items-center justify-between mb-1">
          <p class="text-sm font-semibold text-gray-700">
            {{ field.name }}
            <span v-if="field.isRequired" v-tooltip.top="'Required'" class="text-red-500">*</span>
          </p>
          <div class="flex gap-1">
            <!-- Copy ICD codes to Assessment button -->
            <Button
              v-if="isAssessmentSection(field.name) || isAssessmentAndPlanSection(field.name)"
              icon="pi pi-copy"
              size="small"
              text
              @click="copyIcdCodesToField(field)"
              :disabled="isCopyingCodes || selectedIcdCodes.length === 0"
              :loading="isCopyingCodes"
              v-tooltip.top="'Copy ICD codes'"
            />
            <!-- Copy orders to Plan button -->
            <Button
              v-if="isPlanSection(field.name) || isAssessmentAndPlanSection(field.name)"
              icon="pi pi-list"
              size="small"
              text
              @click="copyOrdersToField(field)"
              :disabled="isCopyingOrders || getActiveAndCompletedOrders().length === 0"
              :loading="isCopyingOrders"
              v-tooltip.top="'Copy orders'"
            />
            <!-- Copy vitals to Objective button -->
            <Button
              v-if="isObjectiveSection(field.name)"
              icon="pi pi-heart"
              size="small"
              text
              @click="copyVitalsToField(field)"
              :disabled="isCopyingVitals || getLatestVitals().length === 0"
              :loading="isCopyingVitals"
              v-tooltip.top="'Copy latest vitals'"
            />
            <!-- Copy lab results to Objective button -->
            <Button
              v-if="isObjectiveSection(field.name)"
              icon="pi pi-chart-line"
              size="small"
              text
              @click="copyLabResultsToField(field)"
              :disabled="isCopyingLabResults || getLatestLabAndImagingResults().length === 0"
              :loading="isCopyingLabResults"
              v-tooltip.top="'Copy latest lab & procedure results'"
            />
            <!-- Copy questionnaires to Subjective button -->
            <Button
              v-if="isSubjectiveSection(field.name)"
              icon="pi pi-file-edit"
              size="small"
              text
              @click="copyQuestionnairesToField(field)"
              :disabled="isCopyingQuestionnaires || getHistoryQuestionnaires().length === 0"
              :loading="isCopyingQuestionnaires"
              v-tooltip.top="'Copy history questionnaires'"
            />
            <!-- Copy vitals to Vitals section button -->
            <Button
              v-if="isVitalsSection(field.name)"
              icon="pi pi-heart"
              size="small"
              text
              @click="copyVitalsToVitalsSection(field)"
              :disabled="isCopyingVitalsToVitalsSection || getLatestVitals().length === 0"
              :loading="isCopyingVitalsToVitalsSection"
              v-tooltip.top="'Copy latest vitals'"
            />
            <!-- Whisper Small model -->
            <VoiceRecordButtonWhisper
              model="small"
              :section-name="`New Note - ${selectedTemplate.name}`"
              :field-name="field.name"
              @transcription-complete="(text) => handleTranscriptionComplete(field, text)"
              @recording-error="handleRecordingError"
              @processing-start="() => handleProcessingStart(field)"
              @processing-end="() => handleProcessingEnd(field)"
            />
          </div>
        </div>

        <!-- Processing indicator for this field -->
        <div v-if="isFieldProcessing(field.name)" class="mb-2 flex items-center text-sm text-blue-600">
          <i class="pi pi-spin pi-spinner mr-2"></i>
          <span>Transcribing audio...</span>
        </div>

        <Textarea
          :id="field.name!"
          label=""
          v-model="field.value"
          class="w-full"
          autoResize
          rows="5"
        />
      </div>
    </div>

    <!-- Disclaimer for transcription -->
    <div class="mt-4 mb-2 text-sm text-gray-600 italic">
      Speech-to-text transcripts are generated automatically and may contain errors. Please review carefully for accuracy before signing this note.
    </div>

    <Button
      severity="success"
      label="Sign Note"
      @click="signNote"
      class="mt-4 px-4 py-2 float-right"
    />
  </div>
</template>

<script setup lang="ts">
import Textarea from '@/components/form-extensions/InputTextAreaFluent.vue'
import SelectFluent from '@/components/form-extensions/SelectFluent.vue'
import VoiceRecordButtonWhisper from '@/components/VoiceRecordButtonWhisper.vue'
import Button from 'primevue/button'
import MultiSelect from 'primevue/multiselect'
import FloatLabel from 'primevue/floatlabel'
import { api } from '@/api'
import { computed, onMounted, ref, watch } from 'vue'
import { useForm } from 'vee-validate'
import type { NoteFieldResponse, CodingResponse, CptCodeResponse } from '@/api/api-reference.ts'
import * as yup from 'yup'
import { useEncounterStore } from '@/stores/encounter.ts'
import debounce from 'lodash.debounce'
import AutoCompleteFluent from '../../components/form-extensions/AutoCompleteFluent.vue'
import { useToast } from 'vue-toastification'
import { getDefaultMeasurements } from '@/utils/vitalSigns.ts'
const toast = useToast()

const encounterStore = useEncounterStore()

const selectedTemplateId = computed(() => encounterStore.selectedTemplateId)
const selectedTemplate = computed(() => encounterStore.selectedTemplate)
const templates = ref<Array<{ label: string; value: string }>>([])

// Medical codes
const selectedIcdCodes = computed(() => encounterStore.selectedIcdCodes)
const selectedCptCode = computed(() => encounterStore.selectedCptCode)
const icdCodeOptions = ref<CodingResponse[]>([])
const cptCodeOptions = ref<CptCodeResponse[]>([])
const icdLoading = ref(false)
const cptLoading = ref(false)
const isCopyingCodes = ref(false)
const isCopyingOrders = ref(false)
const isCopyingVitals = ref(false)
const isCopyingLabResults = ref(false)
const isCopyingQuestionnaires = ref(false)
const isCopyingVitalsToVitalsSection = ref(false)

// Track processing state per field
const processingFields = ref<Set<string>>(new Set())

const emit = defineEmits(['note-signed'])

// Section detection functions
const isAssessmentSection = (fieldName: string | undefined) => {
  return fieldName?.toLowerCase().includes('assessment') ?? false
}

const isPlanSection = (fieldName: string | undefined) => {
  return fieldName?.toLowerCase().includes('plan') ?? false
}

const isAssessmentAndPlanSection = (fieldName: string | undefined) => {
  const name = fieldName?.toLowerCase() ?? ''
  return name.includes('assessment') && name.includes('plan')
}

const isObjectiveSection = (fieldName: string | undefined) => {
  return fieldName?.toLowerCase().includes('objective') ?? false
}

const isSubjectiveSection = (fieldName: string | undefined) => {
  return fieldName?.toLowerCase().includes('subjective') ?? false
}

const isVitalsSection = (fieldName: string | undefined) => {
  return fieldName?.toLowerCase().includes('vital') ?? false
}

// Get orders with Active and Completed status only
const getActiveAndCompletedOrders = () => {
  return encounterStore.encounterOrders.filter(
    (order) => order.status === 'Active' || order.status === 'Completed',
  )
}

// Get latest vitals by most recent date
const getLatestVitals = () => {
  const vitals = encounterStore.vitalSigns
  if (!vitals || vitals.length === 0) return []

  // find the most recent date
  const latestDate = vitals
    .filter((v) => v.recordedDate)
    .map((v) => new Date(v.recordedDate!))
    .sort((a, b) => b.getTime() - a.getTime())[0]

  if (!latestDate) return []

  // return all vitals from that date
  return vitals.filter((v) => {
    const vDate = new Date(v.recordedDate!)
    return vDate.getTime() === latestDate.getTime()
  })
}

// Get latest lab and imaging results with final status
const getLatestLabAndImagingResults = () => {
  const labResults = encounterStore.laboratoryResults.filter(
    (result) => result.status?.toLowerCase() === 'final'
  )
  const imagingResults = encounterStore.imagingResults.filter(
    (result) => result.status?.toLowerCase() === 'final'
  )

  // combine and sort by date
  const allResults = [
    ...labResults.map(r => ({ ...r, type: 'lab', date: r.effectiveDateTime || r.issued })),
    ...imagingResults.map(r => ({ ...r, type: 'imaging', date: r.effectiveDate || r.issued }))
  ].filter(r => r.date)

  if (allResults.length === 0) return []

  // find the most recent date
  const latestDate = allResults
    .map(r => new Date(r.date!))
    .sort((a, b) => b.getTime() - a.getTime())[0]

  // return all results from that date
  return allResults.filter(r => {
    const rDate = new Date(r.date!)
    return rDate.getTime() === latestDate.getTime()
  })
}

// Get questionnaires with type History
const getHistoryQuestionnaires = () => {
  return encounterStore.questionnaires?.filter(
    (questionnaire) => questionnaire.type === 'History'
  ) ?? []
}

// Get ICD description for table display
const getIcdDescription = (code: CodingResponse) => {
  if (code.displayName && code.displayName.includes(' - ')) {
    return code.displayName.split(' - ').slice(1).join(' - ')
  }
  return code.displayName || code.code || ''
}

// Remove ICD code from selected list
const removeIcdCode = (index: number) => {
  selectedIcdCodes.value.splice(index, 1)
  // Update options to reflect current selection
  icdCodeOptions.value = [...selectedIcdCodes.value]
}

// Copy selected ICD codes to specific field
const copyIcdCodesToField = async (field: NoteFieldResponse) => {
  if (selectedIcdCodes.value.length === 0) return

  isCopyingCodes.value = true
  try {
    // Format ICD codes for insertion
    const icdCodesText = selectedIcdCodes.value
      .map((code) => `${code.code} - ${getIcdDescription(code)}`)
      .join('\n')

    // Get existing field text
    const existingText = field.value?.toString().trim() || ''

    // Append ICD codes to field text
    if (existingText) {
      // Add ICD codes at the end with proper spacing
      const separator = existingText.endsWith('.') || existingText.endsWith(':') ? '\n\n' : '.\n\n'
      field.value = existingText + separator + 'ICD-10 Codes:\n' + icdCodesText + '\n'
    } else {
      // If field is empty, just add the ICD codes
      field.value = 'ICD-10 Codes:\n' + icdCodesText + '\n'
    }

    console.log(`Copied ${selectedIcdCodes.value.length} ICD codes to ${field.name} field`)
  } catch (error) {
    console.error('Error copying ICD codes to field:', error)
  } finally {
    isCopyingCodes.value = false
  }
}

// Copy encounter orders to specific field
const copyOrdersToField = async (field: NoteFieldResponse) => {
  const activeAndCompletedOrders = getActiveAndCompletedOrders()
  if (activeAndCompletedOrders.length === 0) return

  isCopyingOrders.value = true
  try {
    // Format orders for insertion
    const ordersText = activeAndCompletedOrders.map((order) => order.name).join('\n')

    // Get existing field text
    const existingText = field.value?.toString().trim() || ''

    // Append orders to field text
    if (existingText) {
      // Add orders at the end with proper spacing
      const separator = existingText.endsWith('.') || existingText.endsWith(':') ? '\n\n' : '.\n\n'
      field.value = existingText + separator + 'Orders:\n' + ordersText + '\n'
    } else {
      // If field is empty, just add the orders
      field.value = 'Orders:\n' + ordersText + '\n'
    }

    console.log(`Copied ${activeAndCompletedOrders.length} orders to ${field.name} field`)
  } catch (error) {
    console.error('Error copying orders to field:', error)
  } finally {
    isCopyingOrders.value = false
  }
}

// Copy latest vitals to specific field
const copyVitalsToField = async (field: NoteFieldResponse) => {
  const latestVitals = getLatestVitals()
  if (latestVitals.length === 0) return

  isCopyingVitals.value = true
  try {
    // Group vitals by creation time and format like in VitalSignBox
    const grouped = new Map<string, typeof latestVitals>()

    for (const vital of latestVitals) {
      const key = vital.createdAt!
      if (!grouped.has(key)) {
        grouped.set(key, [])
      }
      grouped.get(key)!.push(vital)
    }

    const formattedVitals: string[] = []

    for (const [, vitals] of grouped.entries()) {
      const systolic = vitals.find((v) => v.type === 'SystolicBloodPressure')
      const diastolic = vitals.find((v) => v.type === 'DiastolicBloodPressure')

      // Handle blood pressure specially
      if (systolic || diastolic) {
        const bpValue = `${systolic?.value ?? ''}/${diastolic?.value ?? ''}`
        const measurement = getDefaultMeasurements().find(m => m.type === 'BloodPressure')
        formattedVitals.push(`${measurement?.textLong || 'Blood Pressure'}: ${bpValue} ${measurement?.unit || 'mmHg'}`)
      }

      // Handle other vitals
      for (const vital of vitals) {
        if (vital.type !== 'SystolicBloodPressure' && vital.type !== 'DiastolicBloodPressure') {
          const measurement = getDefaultMeasurements().find(m => m.type === vital.type)
          const unit = measurement?.unit || ''
          formattedVitals.push(`${measurement?.textLong || vital.type}: ${vital.value}${unit ? ' ' + unit : ''}`)
        }
      }
    }

    const vitalsText = formattedVitals.join('\n')

    // Get existing field text
    const existingText = field.value?.toString().trim() || ''

    // Append vitals to field text
    if (existingText) {
      // Add vitals at the end with proper spacing
      const separator = existingText.endsWith('.') || existingText.endsWith(':') ? '\n\n' : '.\n\n'
      field.value = existingText + separator + 'Vital Signs:\n' + vitalsText + '\n'
    } else {
      // If field is empty, just add the vitals
      field.value = 'Vital Signs:\n' + vitalsText + '\n'
    }

    console.log(`Copied ${formattedVitals.length} vital signs to ${field.name} field`)
  } catch (error) {
    console.error('Error copying vitals to field:', error)
  } finally {
    isCopyingVitals.value = false
  }
}

// Copy latest lab and imaging results to specific field
const copyLabResultsToField = async (field: NoteFieldResponse) => {
  const latestResults = getLatestLabAndImagingResults()
  if (latestResults.length === 0) return

  isCopyingLabResults.value = true
  try {
    // Format results for insertion
    const resultsText = latestResults
      .map((result) => {
        if (result.type === 'lab') {
          const labResult = result as any // lab result
          const value = labResult.valueQuantity || 'N/A'
          const unit = labResult.valueUnit || ''
          const reference = labResult.referenceRangeLow && labResult.referenceRangeHigh
            ? ` (Ref: ${labResult.referenceRangeLow}-${labResult.referenceRangeHigh}${labResult.referenceRangeUnit || ''})`
            : ''
          return `${labResult.codeDisplay || labResult.code}: ${value}${unit ? ' ' + unit : ''}${reference}`
        } else {
          const imagingResult = result as any // imaging result
          return `${imagingResult.reference || 'Imaging Study'}: Available`
        }
      })
      .join('\n')

    // Get existing field text
    const existingText = field.value?.toString().trim() || ''

    // Append results to field text
    if (existingText) {
      // Add results at the end with proper spacing
      const separator = existingText.endsWith('.') || existingText.endsWith(':') ? '\n\n' : '.\n\n'
      field.value = existingText + separator + 'Lab & Procedure Results:\n' + resultsText + '\n'
    } else {
      // If field is empty, just add the results
      field.value = 'Lab & Procedure Results:\n' + resultsText + '\n'
    }

    console.log(`Copied ${latestResults.length} lab/imaging results to ${field.name} field`)
  } catch (error) {
    console.error('Error copying lab results to field:', error)
  } finally {
    isCopyingLabResults.value = false
  }
}

// Copy questionnaires with type History to specific field
const copyQuestionnairesToField = async (field: NoteFieldResponse) => {
  const historyQuestionnaires = getHistoryQuestionnaires()
  if (historyQuestionnaires.length === 0) return

  isCopyingQuestionnaires.value = true
  try {
    // Format questionnaires for insertion
    const questionnairesText = historyQuestionnaires
      .map((questionnaire) => {
        const title = questionnaire.title || 'History Questionnaire'
        const questions = questionnaire.questions?.map((question) => {
          const answers = question.answers?.join(', ') || 'No answer provided'
          return `${question.text}: ${answers}`
        }).join('\n') || 'No questions available'
        return `${title}:\n${questions}`
      })
      .join('\n\n')

    // Get existing field text
    const existingText = field.value?.toString().trim() || ''

    // Append questionnaires to field text
    if (existingText) {
      // Add questionnaires at the end with proper spacing
      const separator = existingText.endsWith('.') || existingText.endsWith(':') ? '\n\n' : '.\n\n'
      field.value = existingText + separator + 'History Questionnaires:\n' + questionnairesText + '\n'
    } else {
      // If field is empty, just add the questionnaires
      field.value = 'History Questionnaires:\n' + questionnairesText + '\n'
    }

    console.log(`Copied ${historyQuestionnaires.length} history questionnaires to ${field.name} field`)
  } catch (error) {
    console.error('Error copying questionnaires to field:', error)
  } finally {
    isCopyingQuestionnaires.value = false
  }
}

// Copy vitals to vitals section
const copyVitalsToVitalsSection = async (field: NoteFieldResponse) => {
  const latestVitals = getLatestVitals()
  if (latestVitals.length === 0) return

  isCopyingVitalsToVitalsSection.value = true
  try {
    // Group vitals by creation time and format like in VitalSignBox
    const grouped = new Map<string, typeof latestVitals>()

    for (const vital of latestVitals) {
      const key = vital.createdAt!
      if (!grouped.has(key)) {
        grouped.set(key, [])
      }
      grouped.get(key)!.push(vital)
    }

    const formattedVitals: string[] = []

    for (const [, vitals] of grouped.entries()) {
      const systolic = vitals.find((v) => v.type === 'SystolicBloodPressure')
      const diastolic = vitals.find((v) => v.type === 'DiastolicBloodPressure')

      // Handle blood pressure specially
      if (systolic || diastolic) {
        const bpValue = `${systolic?.value ?? ''}/${diastolic?.value ?? ''}`
        const measurement = getDefaultMeasurements().find(m => m.type === 'BloodPressure')
        formattedVitals.push(`${measurement?.textLong || 'Blood Pressure'}: ${bpValue} ${measurement?.unit || 'mmHg'}`)
      }

      // Handle other vitals
      for (const vital of vitals) {
        if (vital.type !== 'SystolicBloodPressure' && vital.type !== 'DiastolicBloodPressure') {
          const measurement = getDefaultMeasurements().find(m => m.type === vital.type)
          const unit = measurement?.unit || ''
          formattedVitals.push(`${measurement?.textLong || vital.type}: ${vital.value}${unit ? ' ' + unit : ''}`)
        }
      }
    }

    const vitalsText = formattedVitals.join('\n')

    // Get existing field text
    const existingText = field.value?.toString().trim() || ''

    // Append vitals to field text
    if (existingText) {
      // Add vitals at the end with proper spacing
      const separator = existingText.endsWith('.') || existingText.endsWith(':') ? '\n\n' : '.\n\n'
      field.value = existingText + separator + 'Vital Signs:\n' + vitalsText + '\n'
    } else {
      // If field is empty, just add the vitals
      field.value = 'Vital Signs:\n' + vitalsText + '\n'
    }

    console.log(`Copied ${formattedVitals.length} vital signs to ${field.name} field`)
  } catch (error) {
    console.error('Error copying vitals to field:', error)
  } finally {
    isCopyingVitalsToVitalsSection.value = false
  }
}

function buildValidationSchema(fields: NoteFieldResponse[]) {
  const shape: Record<string, yup.AnySchema> = {}

  for (const field of fields) {
    let schema: yup.AnySchema = yup.string()
    if (field.isRequired) {
      schema = schema.required('This field is required')
    } else {
      schema = schema.nullable()
    }

    shape[field.name!] = schema
  }

  return yup.object().shape(shape)
}

const validationSchemaNew = computed(() => {
  const fields = selectedTemplate.value?.fields ?? []
  return buildValidationSchema(fields)
})

const { handleSubmit: handleSubmitNew } = useForm({
  validationSchema: validationSchemaNew,
})

const signNote = handleSubmitNew(async (values) => {
  if (!selectedTemplate.value) return
  try {
    await api.notes.noteCreateNote({
      name: selectedTemplate.value.name,
      patientId: encounterStore.selectedEncounter?.patientId,
      classification: selectedTemplate.value.classification,
      documentType: selectedTemplate.value.documentType,
      encounterId: encounterStore.selectedEncounter?.id,
      fields: Object.entries(values)
        .filter(([key]) => key !== 'template')
        .map(([key, value]) => ({
          name: key,
          value: value,
        })),
      icdCodes:
        (selectedIcdCodes.value
          ?.map((code) => code.code)
          .filter((code) => code !== undefined) as string[]) ?? [],
      cptCode: selectedCptCode.value?.code,
    })

    // Reset form
    encounterStore.setSelectedTemplateId('')
    encounterStore.setSelectedTemplate(undefined)
    encounterStore.setSelectedIcdCodes([])
    encounterStore.setSelectedCptCode(null)

    toast.success('Note created successfully')
    emit('note-signed')
  } catch (error) {
    console.error('Failed to create note', error)
  }
})

// Most commonly used CPT codes
const commonCptCodes: CptCodeResponse[] = [
  {
    id: '1',
    code: '99213',
    conceptId: '',
    clinicianDescriptor: '99213 - Office visit, established patient, 15 min',
  },
  {
    id: '2',
    code: '99214',
    conceptId: '',
    clinicianDescriptor: '99214 - Office visit, established patient, 25 min',
  },
  {
    id: '3',
    code: '99215',
    conceptId: '',
    clinicianDescriptor: '99215 - Office visit, established patient, 40 min',
  },
  {
    id: '4',
    code: '99203',
    conceptId: '',
    clinicianDescriptor: '99203 - Office visit, new patient, 30 min',
  },
  {
    id: '5',
    code: '99204',
    conceptId: '',
    clinicianDescriptor: '99204 - Office visit, new patient, 45 min',
  },
  {
    id: '6',
    code: '99205',
    conceptId: '',
    clinicianDescriptor: '99205 - Office visit, new patient, 60 min',
  },
  {
    id: '7',
    code: '99212',
    conceptId: '',
    clinicianDescriptor: '99212 - Office visit, established patient, 10 min',
  },
  {
    id: '8',
    code: '99202',
    conceptId: '',
    clinicianDescriptor: '99202 - Office visit, new patient, 20 min',
  },
  {
    id: '9',
    code: '99211',
    conceptId: '',
    clinicianDescriptor: '99211 - Office visit, established patient, 5 min',
  },
  {
    id: '10',
    code: '99201',
    conceptId: '',
    clinicianDescriptor: '99201 - Office visit, new patient, 10 min',
  },
]

// Load common CPT codes when dropdown is clicked
const loadCommonCptCodes = () => {
  console.log('usao')

  cptCodeOptions.value = [...commonCptCodes]
}

// Load common ICD codes when dropdown is opened
const loadCommonIcdCodes = async () => {
  if (icdCodeOptions.value.length === 0) {
    // Load some common ICD codes for initial display
    try {
      const response = await api.icd10.icd10ListIcd10({
        searchParam: '',
        pageNumber: 1,
        pageSize: 20,
      })

      const commonCodes =
        response.data.items?.map((x) => ({
          id: x.id,
          code: x.code,
          displayName: `${x.code} - ${x.displayName}`,
          codeSystem: x.codeSystem,
          codeSystemName: x.codeSystemName,
          codeSystemVersion: x.codeSystemVersion,
        })) ?? []

      // Merge with currently selected codes
      const allCodes = [...selectedIcdCodes.value, ...commonCodes]
      const uniqueCodes = allCodes.filter(
        (code, index, self) => index === self.findIndex((c) => c.code === code.code),
      )

      icdCodeOptions.value = uniqueCodes
    } catch (error) {
      console.error('Error loading common ICD codes:', error)
      icdCodeOptions.value = [...selectedIcdCodes.value]
    }
  }
}

// ICD-10 Code Search
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const searchIcdCodes = debounce(async (event: any) => {
  await fetchIcdCodes(event.value)
}, 300)

const fetchIcdCodes = async (query: string) => {
  if (!query.trim() || query.length < 2) {
    // When no query, show currently selected codes plus any loaded common codes
    if (icdCodeOptions.value.length === 0) {
      await loadCommonIcdCodes()
    } else {
      // Ensure selected codes are always visible
      const allCodes = [...selectedIcdCodes.value, ...icdCodeOptions.value]
      const uniqueCodes = allCodes.filter(
        (code, index, self) => index === self.findIndex((c) => c.code === code.code),
      )
      icdCodeOptions.value = uniqueCodes
    }
    return
  }

  icdLoading.value = true
  try {
    const response = await api.icd10.icd10ListIcd10({
      searchParam: query,
      pageNumber: 1,
      pageSize: 20,
    })

    const searchResults =
      response.data.items?.map((x) => ({
        id: x.id,
        code: x.code,
        displayName: `${x.code} - ${x.displayName}`,
        codeSystem: x.codeSystem,
        codeSystemName: x.codeSystemName,
        codeSystemVersion: x.codeSystemVersion,
      })) ?? []

    // Merge currently selected codes with search results, removing duplicates
    const allCodes = [...selectedIcdCodes.value, ...searchResults]
    const uniqueCodes = allCodes.filter(
      (code, index, self) => index === self.findIndex((c) => c.code === code.code),
    )

    icdCodeOptions.value = uniqueCodes
  } catch (error) {
    console.error('Error fetching ICD codes:', error)
    icdCodeOptions.value = [...selectedIcdCodes.value]
  } finally {
    icdLoading.value = false
  }
}

const hasMore = ref(true)
const fetchCptCodes = async (query: string, page: number, reset = false) => {
  console.log(query)
  if (!query.trim() || query.length < 2) {
    // If no query, show common codes
    cptCodeOptions.value = [...commonCptCodes]
    return
  }
  cptLoading.value = true

  const response = await api.cptcodes.cptCodesListCptCodes({
    searchParam: query,
    pageNumber: page,
  })

  if (reset) cptCodeOptions.value = []
  cptCodeOptions.value = [
    ...cptCodeOptions.value,
    ...(response.data.items?.map((x) => ({
      id: x.id,
      code: x.code,
      conceptId: x.conceptId,
      clinicianDescriptor: `${x.code} - ${x.clinicianDescriptor}`,
    })) ?? []),
  ]

  hasMore.value = cptCodeOptions.value.length < (response.data.totalItems ?? 0)
  cptLoading.value = false
}

onMounted(async () => {
  // Load note templates
  const result = await api.noteTemplates.noteTemplateListNoteTemplates({
    pageNumber: 1,
    pageSize: 100,
    searchParam: '',
    specialtyFilter: '',
  })
  templates.value =
    result.data.items?.map((x) => ({
      label: `${x.name}`,
      value: `${x.id}`,
    })) ?? []

  // Initialize ICD codes with some common options
  await loadCommonIcdCodes()
})

watch(
  () => selectedTemplateId.value,
  async (newSelectedTemplateId) => {
    // selectedTemplate.value = (
    //   await api.noteTemplates.noteTemplateGetNoteTemplate(newSelectedTemplateId)
    // ).data
    const response = await api.noteTemplates.noteTemplateGetNoteTemplate(newSelectedTemplateId)
    encounterStore.setSelectedTemplate(response.data)
  },
)

const handleRecordingError = (error: string) => {
  console.error('Voice recording error:', error)
  // TODO: Add user-friendly error notification when toast/notification system is available
}

const handleProcessingStart = (field: NoteFieldResponse) => {
  if (field.name) {
    processingFields.value.add(field.name)
  }
}

const handleProcessingEnd = (field: NoteFieldResponse) => {
  if (field.name) {
    processingFields.value.delete(field.name)
  }
}

const isFieldProcessing = (fieldName: string | undefined) => {
  return fieldName ? processingFields.value.has(fieldName) : false
}

const handleTranscriptionComplete = (field: NoteFieldResponse, finalText: string) => {
  // Append the final transcription to the existing field content
  if (field.value && field.value.toString().trim()) {
    const existingText = field.value.toString().trim()
    // Add new line for new transcription
    field.value = existingText + '\n' + finalText
  } else {
    field.value = finalText
  }

  console.log(`Transcription completed for field "${field.name}":`, finalText)
}
</script>
